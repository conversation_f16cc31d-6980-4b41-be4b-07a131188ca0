{"name": "Project", "type": "ARM", "dependenceList": [], "srcDirs": [], "virtualFolder": {"name": "<virtual_root>", "files": [], "folders": [{"name": "Library", "files": [{"path": "Library/stm32f10x_adc.c"}, {"path": "Library/misc.c"}, {"path": "Library/stm32f10x_bkp.c"}, {"path": "Library/stm32f10x_can.c"}, {"path": "Library/stm32f10x_cec.c"}, {"path": "Library/stm32f10x_crc.c"}, {"path": "Library/stm32f10x_dac.c"}, {"path": "Library/stm32f10x_dbgmcu.c"}, {"path": "Library/stm32f10x_dma.c"}, {"path": "Library/stm32f10x_exti.c"}, {"path": "Library/stm32f10x_flash.c"}, {"path": "Library/stm32f10x_fsmc.c"}, {"path": "Library/stm32f10x_gpio.c"}, {"path": "Library/stm32f10x_i2c.c"}, {"path": "Library/stm32f10x_iwdg.c"}, {"path": "Library/stm32f10x_pwr.c"}, {"path": "Library/stm32f10x_rcc.c"}, {"path": "Library/stm32f10x_rtc.c"}, {"path": "Library/stm32f10x_sdio.c"}, {"path": "Library/stm32f10x_spi.c"}, {"path": "Library/stm32f10x_tim.c"}, {"path": "Library/stm32f10x_usart.c"}, {"path": "Library/stm32f10x_wwdg.c"}], "folders": []}, {"name": "Start", "files": [{"path": "Start/startup_stm32f10x_md.s"}, {"path": "Start/stm32f10x.h"}, {"path": "Start/system_stm32f10x.c"}, {"path": "Start/system_stm32f10x.h"}, {"path": "Start/core_cm3.h"}, {"path": "Start/core_cm3.c"}], "folders": []}, {"name": "User", "files": [{"path": "User/main.c"}, {"path": "User/stm32f10x_conf.h"}, {"path": "User/stm32f10x_it.c"}, {"path": "User/stm32f10x_it.h"}], "folders": []}, {"name": "Driver", "files": [{"path": "Driver/gpio_driver.h"}, {"path": "Driver/gpio_driver.c"}, {"path": "Driver/pwm.c"}, {"path": "Driver/pwm.h"}, {"path": "Driver/usart.c"}, {"path": "Driver/usart.h"}, {"path": "Driver/timer.c"}, {"path": "Driver/timer.h"}, {"path": "Driver/encoder.c"}, {"path": "Driver/encoder.h"}, {"path": "Driver/pid.c"}, {"path": "Driver/pid.h"}, {"path": "Driver/OLED.c"}, {"path": "Driver/OLED.h"}, {"path": "Driver/OLED_Data.c"}, {"path": "Driver/OLED_Data.h"}, {"path": "Driver/motor_control.c"}, {"path": "Driver/motor_control.h"}], "folders": []}]}, "outDir": "build", "deviceName": null, "packDir": null, "miscInfo": {"uid": "3af2bc7fe513928779137d44417b6bf5"}, "targets": {"Target 1": {"excludeList": [], "toolchain": "AC5", "compileConfig": {"cpuType": "Cortex-M3", "archExtensions": "", "floatingPointHardware": "none", "scatterFilePath": "", "useCustomScatterFile": false, "storageLayout": {"RAM": [{"tag": "RAM", "id": 1, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "RAM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "RAM", "id": 3, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "IRAM", "id": 1, "mem": {"startAddr": "0x20000000", "size": "0x5000"}, "isChecked": true, "noInit": false}, {"tag": "IRAM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}], "ROM": [{"tag": "ROM", "id": 1, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "ROM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "ROM", "id": 3, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "IROM", "id": 1, "mem": {"startAddr": "0x8000000", "size": "0x10000"}, "isChecked": true, "isStartup": true}, {"tag": "IROM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}]}, "options": "null"}, "uploader": "JLink", "uploadConfig": {"bin": "", "baseAddr": "", "cpuInfo": {"vendor": "null", "cpuName": "null"}, "proType": 1, "speed": 8000, "otherCmds": ""}, "uploadConfigMap": {}, "custom_dep": {"name": "default", "incList": ["Start", "User", "Library", "Driver", ".cmsis/include", "RTE/_Target 1"], "libList": [], "defineList": ["USE_STDPERIPH_DRIVER"]}, "builderOptions": {"AC5": {"version": 4, "beforeBuildTasks": [], "afterBuildTasks": [], "global": {"use-microLIB": false, "output-debug-info": "enable"}, "c/cpp-compiler": {"optimization": "level-0", "one-elf-section-per-function": true, "c99-mode": true, "C_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "CXX_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "warnings": "all-warnings"}, "asm-compiler": {}, "linker": {"output-format": "elf", "xo-base": "", "ro-base": "0x08000000", "rw-base": "0x20000000"}}}}}, "version": "3.6"}