Component: Arm Compiler for Embedded 6.22 Tool: armlink [5ee90200]

==============================================================================

Section Cross References

    stm32f10x_adc.o(.text.ADC_DeInit) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_adc.o(.ARM.exidx.text.ADC_DeInit) refers to stm32f10x_adc.o(.text.ADC_DeInit) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_Init) refers to stm32f10x_adc.o(.text.ADC_Init) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_StructInit) refers to stm32f10x_adc.o(.text.ADC_StructInit) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_Cmd) refers to stm32f10x_adc.o(.text.ADC_Cmd) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_DMACmd) refers to stm32f10x_adc.o(.text.ADC_DMACmd) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_ITConfig) refers to stm32f10x_adc.o(.text.ADC_ITConfig) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_ResetCalibration) refers to stm32f10x_adc.o(.text.ADC_ResetCalibration) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_GetResetCalibrationStatus) refers to stm32f10x_adc.o(.text.ADC_GetResetCalibrationStatus) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_StartCalibration) refers to stm32f10x_adc.o(.text.ADC_StartCalibration) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_GetCalibrationStatus) refers to stm32f10x_adc.o(.text.ADC_GetCalibrationStatus) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_SoftwareStartConvCmd) refers to stm32f10x_adc.o(.text.ADC_SoftwareStartConvCmd) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_GetSoftwareStartConvStatus) refers to stm32f10x_adc.o(.text.ADC_GetSoftwareStartConvStatus) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_DiscModeChannelCountConfig) refers to stm32f10x_adc.o(.text.ADC_DiscModeChannelCountConfig) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_DiscModeCmd) refers to stm32f10x_adc.o(.text.ADC_DiscModeCmd) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_RegularChannelConfig) refers to stm32f10x_adc.o(.text.ADC_RegularChannelConfig) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_ExternalTrigConvCmd) refers to stm32f10x_adc.o(.text.ADC_ExternalTrigConvCmd) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_GetConversionValue) refers to stm32f10x_adc.o(.text.ADC_GetConversionValue) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_GetDualModeConversionValue) refers to stm32f10x_adc.o(.text.ADC_GetDualModeConversionValue) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_AutoInjectedConvCmd) refers to stm32f10x_adc.o(.text.ADC_AutoInjectedConvCmd) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_InjectedDiscModeCmd) refers to stm32f10x_adc.o(.text.ADC_InjectedDiscModeCmd) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_ExternalTrigInjectedConvConfig) refers to stm32f10x_adc.o(.text.ADC_ExternalTrigInjectedConvConfig) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_ExternalTrigInjectedConvCmd) refers to stm32f10x_adc.o(.text.ADC_ExternalTrigInjectedConvCmd) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_SoftwareStartInjectedConvCmd) refers to stm32f10x_adc.o(.text.ADC_SoftwareStartInjectedConvCmd) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_GetSoftwareStartInjectedConvCmdStatus) refers to stm32f10x_adc.o(.text.ADC_GetSoftwareStartInjectedConvCmdStatus) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_InjectedChannelConfig) refers to stm32f10x_adc.o(.text.ADC_InjectedChannelConfig) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_InjectedSequencerLengthConfig) refers to stm32f10x_adc.o(.text.ADC_InjectedSequencerLengthConfig) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_SetInjectedOffset) refers to stm32f10x_adc.o(.text.ADC_SetInjectedOffset) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_GetInjectedConversionValue) refers to stm32f10x_adc.o(.text.ADC_GetInjectedConversionValue) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_AnalogWatchdogCmd) refers to stm32f10x_adc.o(.text.ADC_AnalogWatchdogCmd) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_AnalogWatchdogThresholdsConfig) refers to stm32f10x_adc.o(.text.ADC_AnalogWatchdogThresholdsConfig) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_AnalogWatchdogSingleChannelConfig) refers to stm32f10x_adc.o(.text.ADC_AnalogWatchdogSingleChannelConfig) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_TempSensorVrefintCmd) refers to stm32f10x_adc.o(.text.ADC_TempSensorVrefintCmd) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_GetFlagStatus) refers to stm32f10x_adc.o(.text.ADC_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_ClearFlag) refers to stm32f10x_adc.o(.text.ADC_ClearFlag) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_GetITStatus) refers to stm32f10x_adc.o(.text.ADC_GetITStatus) for [Anonymous Symbol]
    stm32f10x_adc.o(.ARM.exidx.text.ADC_ClearITPendingBit) refers to stm32f10x_adc.o(.text.ADC_ClearITPendingBit) for [Anonymous Symbol]
    misc.o(.ARM.exidx.text.NVIC_PriorityGroupConfig) refers to misc.o(.text.NVIC_PriorityGroupConfig) for [Anonymous Symbol]
    misc.o(.ARM.exidx.text.NVIC_Init) refers to misc.o(.text.NVIC_Init) for [Anonymous Symbol]
    misc.o(.ARM.exidx.text.NVIC_SetVectorTable) refers to misc.o(.text.NVIC_SetVectorTable) for [Anonymous Symbol]
    misc.o(.ARM.exidx.text.NVIC_SystemLPConfig) refers to misc.o(.text.NVIC_SystemLPConfig) for [Anonymous Symbol]
    misc.o(.ARM.exidx.text.SysTick_CLKSourceConfig) refers to misc.o(.text.SysTick_CLKSourceConfig) for [Anonymous Symbol]
    stm32f10x_bkp.o(.text.BKP_DeInit) refers to stm32f10x_rcc.o(.text.RCC_BackupResetCmd) for RCC_BackupResetCmd
    stm32f10x_bkp.o(.ARM.exidx.text.BKP_DeInit) refers to stm32f10x_bkp.o(.text.BKP_DeInit) for [Anonymous Symbol]
    stm32f10x_bkp.o(.ARM.exidx.text.BKP_TamperPinLevelConfig) refers to stm32f10x_bkp.o(.text.BKP_TamperPinLevelConfig) for [Anonymous Symbol]
    stm32f10x_bkp.o(.ARM.exidx.text.BKP_TamperPinCmd) refers to stm32f10x_bkp.o(.text.BKP_TamperPinCmd) for [Anonymous Symbol]
    stm32f10x_bkp.o(.ARM.exidx.text.BKP_ITConfig) refers to stm32f10x_bkp.o(.text.BKP_ITConfig) for [Anonymous Symbol]
    stm32f10x_bkp.o(.ARM.exidx.text.BKP_RTCOutputConfig) refers to stm32f10x_bkp.o(.text.BKP_RTCOutputConfig) for [Anonymous Symbol]
    stm32f10x_bkp.o(.ARM.exidx.text.BKP_SetRTCCalibrationValue) refers to stm32f10x_bkp.o(.text.BKP_SetRTCCalibrationValue) for [Anonymous Symbol]
    stm32f10x_bkp.o(.ARM.exidx.text.BKP_WriteBackupRegister) refers to stm32f10x_bkp.o(.text.BKP_WriteBackupRegister) for [Anonymous Symbol]
    stm32f10x_bkp.o(.ARM.exidx.text.BKP_ReadBackupRegister) refers to stm32f10x_bkp.o(.text.BKP_ReadBackupRegister) for [Anonymous Symbol]
    stm32f10x_bkp.o(.ARM.exidx.text.BKP_GetFlagStatus) refers to stm32f10x_bkp.o(.text.BKP_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_bkp.o(.ARM.exidx.text.BKP_ClearFlag) refers to stm32f10x_bkp.o(.text.BKP_ClearFlag) for [Anonymous Symbol]
    stm32f10x_bkp.o(.ARM.exidx.text.BKP_GetITStatus) refers to stm32f10x_bkp.o(.text.BKP_GetITStatus) for [Anonymous Symbol]
    stm32f10x_bkp.o(.ARM.exidx.text.BKP_ClearITPendingBit) refers to stm32f10x_bkp.o(.text.BKP_ClearITPendingBit) for [Anonymous Symbol]
    stm32f10x_can.o(.text.CAN_DeInit) refers to stm32f10x_rcc.o(.text.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_can.o(.ARM.exidx.text.CAN_DeInit) refers to stm32f10x_can.o(.text.CAN_DeInit) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_Init) refers to stm32f10x_can.o(.text.CAN_Init) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_FilterInit) refers to stm32f10x_can.o(.text.CAN_FilterInit) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_StructInit) refers to stm32f10x_can.o(.text.CAN_StructInit) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_SlaveStartBank) refers to stm32f10x_can.o(.text.CAN_SlaveStartBank) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_DBGFreeze) refers to stm32f10x_can.o(.text.CAN_DBGFreeze) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_TTComModeCmd) refers to stm32f10x_can.o(.text.CAN_TTComModeCmd) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_Transmit) refers to stm32f10x_can.o(.text.CAN_Transmit) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_TransmitStatus) refers to stm32f10x_can.o(.text.CAN_TransmitStatus) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_CancelTransmit) refers to stm32f10x_can.o(.text.CAN_CancelTransmit) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_Receive) refers to stm32f10x_can.o(.text.CAN_Receive) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_FIFORelease) refers to stm32f10x_can.o(.text.CAN_FIFORelease) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_MessagePending) refers to stm32f10x_can.o(.text.CAN_MessagePending) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_OperatingModeRequest) refers to stm32f10x_can.o(.text.CAN_OperatingModeRequest) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_Sleep) refers to stm32f10x_can.o(.text.CAN_Sleep) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_WakeUp) refers to stm32f10x_can.o(.text.CAN_WakeUp) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_GetLastErrorCode) refers to stm32f10x_can.o(.text.CAN_GetLastErrorCode) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_GetReceiveErrorCounter) refers to stm32f10x_can.o(.text.CAN_GetReceiveErrorCounter) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_GetLSBTransmitErrorCounter) refers to stm32f10x_can.o(.text.CAN_GetLSBTransmitErrorCounter) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_ITConfig) refers to stm32f10x_can.o(.text.CAN_ITConfig) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_GetFlagStatus) refers to stm32f10x_can.o(.text.CAN_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_ClearFlag) refers to stm32f10x_can.o(.text.CAN_ClearFlag) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_GetITStatus) refers to stm32f10x_can.o(.text.CAN_GetITStatus) for [Anonymous Symbol]
    stm32f10x_can.o(.ARM.exidx.text.CAN_ClearITPendingBit) refers to stm32f10x_can.o(.text.CAN_ClearITPendingBit) for [Anonymous Symbol]
    stm32f10x_cec.o(.text.CEC_DeInit) refers to stm32f10x_rcc.o(.text.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_cec.o(.ARM.exidx.text.CEC_DeInit) refers to stm32f10x_cec.o(.text.CEC_DeInit) for [Anonymous Symbol]
    stm32f10x_cec.o(.ARM.exidx.text.CEC_Init) refers to stm32f10x_cec.o(.text.CEC_Init) for [Anonymous Symbol]
    stm32f10x_cec.o(.ARM.exidx.text.CEC_Cmd) refers to stm32f10x_cec.o(.text.CEC_Cmd) for [Anonymous Symbol]
    stm32f10x_cec.o(.ARM.exidx.text.CEC_ITConfig) refers to stm32f10x_cec.o(.text.CEC_ITConfig) for [Anonymous Symbol]
    stm32f10x_cec.o(.ARM.exidx.text.CEC_OwnAddressConfig) refers to stm32f10x_cec.o(.text.CEC_OwnAddressConfig) for [Anonymous Symbol]
    stm32f10x_cec.o(.ARM.exidx.text.CEC_SetPrescaler) refers to stm32f10x_cec.o(.text.CEC_SetPrescaler) for [Anonymous Symbol]
    stm32f10x_cec.o(.ARM.exidx.text.CEC_SendDataByte) refers to stm32f10x_cec.o(.text.CEC_SendDataByte) for [Anonymous Symbol]
    stm32f10x_cec.o(.ARM.exidx.text.CEC_ReceiveDataByte) refers to stm32f10x_cec.o(.text.CEC_ReceiveDataByte) for [Anonymous Symbol]
    stm32f10x_cec.o(.ARM.exidx.text.CEC_StartOfMessage) refers to stm32f10x_cec.o(.text.CEC_StartOfMessage) for [Anonymous Symbol]
    stm32f10x_cec.o(.ARM.exidx.text.CEC_EndOfMessageCmd) refers to stm32f10x_cec.o(.text.CEC_EndOfMessageCmd) for [Anonymous Symbol]
    stm32f10x_cec.o(.ARM.exidx.text.CEC_GetFlagStatus) refers to stm32f10x_cec.o(.text.CEC_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_cec.o(.ARM.exidx.text.CEC_ClearFlag) refers to stm32f10x_cec.o(.text.CEC_ClearFlag) for [Anonymous Symbol]
    stm32f10x_cec.o(.ARM.exidx.text.CEC_GetITStatus) refers to stm32f10x_cec.o(.text.CEC_GetITStatus) for [Anonymous Symbol]
    stm32f10x_cec.o(.ARM.exidx.text.CEC_ClearITPendingBit) refers to stm32f10x_cec.o(.text.CEC_ClearITPendingBit) for [Anonymous Symbol]
    stm32f10x_crc.o(.ARM.exidx.text.CRC_ResetDR) refers to stm32f10x_crc.o(.text.CRC_ResetDR) for [Anonymous Symbol]
    stm32f10x_crc.o(.ARM.exidx.text.CRC_CalcCRC) refers to stm32f10x_crc.o(.text.CRC_CalcCRC) for [Anonymous Symbol]
    stm32f10x_crc.o(.ARM.exidx.text.CRC_CalcBlockCRC) refers to stm32f10x_crc.o(.text.CRC_CalcBlockCRC) for [Anonymous Symbol]
    stm32f10x_crc.o(.ARM.exidx.text.CRC_GetCRC) refers to stm32f10x_crc.o(.text.CRC_GetCRC) for [Anonymous Symbol]
    stm32f10x_crc.o(.ARM.exidx.text.CRC_SetIDRegister) refers to stm32f10x_crc.o(.text.CRC_SetIDRegister) for [Anonymous Symbol]
    stm32f10x_crc.o(.ARM.exidx.text.CRC_GetIDRegister) refers to stm32f10x_crc.o(.text.CRC_GetIDRegister) for [Anonymous Symbol]
    stm32f10x_dac.o(.text.DAC_DeInit) refers to stm32f10x_rcc.o(.text.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_dac.o(.ARM.exidx.text.DAC_DeInit) refers to stm32f10x_dac.o(.text.DAC_DeInit) for [Anonymous Symbol]
    stm32f10x_dac.o(.ARM.exidx.text.DAC_Init) refers to stm32f10x_dac.o(.text.DAC_Init) for [Anonymous Symbol]
    stm32f10x_dac.o(.ARM.exidx.text.DAC_StructInit) refers to stm32f10x_dac.o(.text.DAC_StructInit) for [Anonymous Symbol]
    stm32f10x_dac.o(.ARM.exidx.text.DAC_Cmd) refers to stm32f10x_dac.o(.text.DAC_Cmd) for [Anonymous Symbol]
    stm32f10x_dac.o(.ARM.exidx.text.DAC_DMACmd) refers to stm32f10x_dac.o(.text.DAC_DMACmd) for [Anonymous Symbol]
    stm32f10x_dac.o(.ARM.exidx.text.DAC_SoftwareTriggerCmd) refers to stm32f10x_dac.o(.text.DAC_SoftwareTriggerCmd) for [Anonymous Symbol]
    stm32f10x_dac.o(.ARM.exidx.text.DAC_DualSoftwareTriggerCmd) refers to stm32f10x_dac.o(.text.DAC_DualSoftwareTriggerCmd) for [Anonymous Symbol]
    stm32f10x_dac.o(.ARM.exidx.text.DAC_WaveGenerationCmd) refers to stm32f10x_dac.o(.text.DAC_WaveGenerationCmd) for [Anonymous Symbol]
    stm32f10x_dac.o(.ARM.exidx.text.DAC_SetChannel1Data) refers to stm32f10x_dac.o(.text.DAC_SetChannel1Data) for [Anonymous Symbol]
    stm32f10x_dac.o(.ARM.exidx.text.DAC_SetChannel2Data) refers to stm32f10x_dac.o(.text.DAC_SetChannel2Data) for [Anonymous Symbol]
    stm32f10x_dac.o(.ARM.exidx.text.DAC_SetDualChannelData) refers to stm32f10x_dac.o(.text.DAC_SetDualChannelData) for [Anonymous Symbol]
    stm32f10x_dac.o(.ARM.exidx.text.DAC_GetDataOutputValue) refers to stm32f10x_dac.o(.text.DAC_GetDataOutputValue) for [Anonymous Symbol]
    stm32f10x_dbgmcu.o(.ARM.exidx.text.DBGMCU_GetREVID) refers to stm32f10x_dbgmcu.o(.text.DBGMCU_GetREVID) for [Anonymous Symbol]
    stm32f10x_dbgmcu.o(.ARM.exidx.text.DBGMCU_GetDEVID) refers to stm32f10x_dbgmcu.o(.text.DBGMCU_GetDEVID) for [Anonymous Symbol]
    stm32f10x_dbgmcu.o(.ARM.exidx.text.DBGMCU_Config) refers to stm32f10x_dbgmcu.o(.text.DBGMCU_Config) for [Anonymous Symbol]
    stm32f10x_dma.o(.ARM.exidx.text.DMA_DeInit) refers to stm32f10x_dma.o(.text.DMA_DeInit) for [Anonymous Symbol]
    stm32f10x_dma.o(.ARM.exidx.text.DMA_Init) refers to stm32f10x_dma.o(.text.DMA_Init) for [Anonymous Symbol]
    stm32f10x_dma.o(.text.DMA_StructInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    stm32f10x_dma.o(.ARM.exidx.text.DMA_StructInit) refers to stm32f10x_dma.o(.text.DMA_StructInit) for [Anonymous Symbol]
    stm32f10x_dma.o(.ARM.exidx.text.DMA_Cmd) refers to stm32f10x_dma.o(.text.DMA_Cmd) for [Anonymous Symbol]
    stm32f10x_dma.o(.ARM.exidx.text.DMA_ITConfig) refers to stm32f10x_dma.o(.text.DMA_ITConfig) for [Anonymous Symbol]
    stm32f10x_dma.o(.ARM.exidx.text.DMA_SetCurrDataCounter) refers to stm32f10x_dma.o(.text.DMA_SetCurrDataCounter) for [Anonymous Symbol]
    stm32f10x_dma.o(.ARM.exidx.text.DMA_GetCurrDataCounter) refers to stm32f10x_dma.o(.text.DMA_GetCurrDataCounter) for [Anonymous Symbol]
    stm32f10x_dma.o(.ARM.exidx.text.DMA_GetFlagStatus) refers to stm32f10x_dma.o(.text.DMA_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_dma.o(.ARM.exidx.text.DMA_ClearFlag) refers to stm32f10x_dma.o(.text.DMA_ClearFlag) for [Anonymous Symbol]
    stm32f10x_dma.o(.ARM.exidx.text.DMA_GetITStatus) refers to stm32f10x_dma.o(.text.DMA_GetITStatus) for [Anonymous Symbol]
    stm32f10x_dma.o(.ARM.exidx.text.DMA_ClearITPendingBit) refers to stm32f10x_dma.o(.text.DMA_ClearITPendingBit) for [Anonymous Symbol]
    stm32f10x_exti.o(.ARM.exidx.text.EXTI_DeInit) refers to stm32f10x_exti.o(.text.EXTI_DeInit) for [Anonymous Symbol]
    stm32f10x_exti.o(.ARM.exidx.text.EXTI_Init) refers to stm32f10x_exti.o(.text.EXTI_Init) for [Anonymous Symbol]
    stm32f10x_exti.o(.ARM.exidx.text.EXTI_StructInit) refers to stm32f10x_exti.o(.text.EXTI_StructInit) for [Anonymous Symbol]
    stm32f10x_exti.o(.ARM.exidx.text.EXTI_GenerateSWInterrupt) refers to stm32f10x_exti.o(.text.EXTI_GenerateSWInterrupt) for [Anonymous Symbol]
    stm32f10x_exti.o(.ARM.exidx.text.EXTI_GetFlagStatus) refers to stm32f10x_exti.o(.text.EXTI_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_exti.o(.ARM.exidx.text.EXTI_ClearFlag) refers to stm32f10x_exti.o(.text.EXTI_ClearFlag) for [Anonymous Symbol]
    stm32f10x_exti.o(.ARM.exidx.text.EXTI_GetITStatus) refers to stm32f10x_exti.o(.text.EXTI_GetITStatus) for [Anonymous Symbol]
    stm32f10x_exti.o(.ARM.exidx.text.EXTI_ClearITPendingBit) refers to stm32f10x_exti.o(.text.EXTI_ClearITPendingBit) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_SetLatency) refers to stm32f10x_flash.o(.text.FLASH_SetLatency) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_HalfCycleAccessCmd) refers to stm32f10x_flash.o(.text.FLASH_HalfCycleAccessCmd) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_PrefetchBufferCmd) refers to stm32f10x_flash.o(.text.FLASH_PrefetchBufferCmd) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_Unlock) refers to stm32f10x_flash.o(.text.FLASH_Unlock) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_UnlockBank1) refers to stm32f10x_flash.o(.text.FLASH_UnlockBank1) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_Lock) refers to stm32f10x_flash.o(.text.FLASH_Lock) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_LockBank1) refers to stm32f10x_flash.o(.text.FLASH_LockBank1) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_ErasePage) refers to stm32f10x_flash.o(.text.FLASH_ErasePage) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_WaitForLastOperation) refers to stm32f10x_flash.o(.text.FLASH_WaitForLastOperation) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_EraseAllPages) refers to stm32f10x_flash.o(.text.FLASH_EraseAllPages) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_EraseAllBank1Pages) refers to stm32f10x_flash.o(.text.FLASH_EraseAllBank1Pages) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_WaitForLastBank1Operation) refers to stm32f10x_flash.o(.text.FLASH_WaitForLastBank1Operation) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_EraseOptionBytes) refers to stm32f10x_flash.o(.text.FLASH_EraseOptionBytes) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_GetReadOutProtectionStatus) refers to stm32f10x_flash.o(.text.FLASH_GetReadOutProtectionStatus) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_ProgramWord) refers to stm32f10x_flash.o(.text.FLASH_ProgramWord) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_ProgramHalfWord) refers to stm32f10x_flash.o(.text.FLASH_ProgramHalfWord) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_ProgramOptionByteData) refers to stm32f10x_flash.o(.text.FLASH_ProgramOptionByteData) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_EnableWriteProtection) refers to stm32f10x_flash.o(.text.FLASH_EnableWriteProtection) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_ReadOutProtection) refers to stm32f10x_flash.o(.text.FLASH_ReadOutProtection) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_UserOptionByteConfig) refers to stm32f10x_flash.o(.text.FLASH_UserOptionByteConfig) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_GetUserOptionByte) refers to stm32f10x_flash.o(.text.FLASH_GetUserOptionByte) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_GetWriteProtectionOptionByte) refers to stm32f10x_flash.o(.text.FLASH_GetWriteProtectionOptionByte) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_GetPrefetchBufferStatus) refers to stm32f10x_flash.o(.text.FLASH_GetPrefetchBufferStatus) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_ITConfig) refers to stm32f10x_flash.o(.text.FLASH_ITConfig) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_GetFlagStatus) refers to stm32f10x_flash.o(.text.FLASH_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_ClearFlag) refers to stm32f10x_flash.o(.text.FLASH_ClearFlag) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_GetStatus) refers to stm32f10x_flash.o(.text.FLASH_GetStatus) for [Anonymous Symbol]
    stm32f10x_flash.o(.ARM.exidx.text.FLASH_GetBank1Status) refers to stm32f10x_flash.o(.text.FLASH_GetBank1Status) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NORSRAMDeInit) refers to stm32f10x_fsmc.o(.text.FSMC_NORSRAMDeInit) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NANDDeInit) refers to stm32f10x_fsmc.o(.text.FSMC_NANDDeInit) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_PCCARDDeInit) refers to stm32f10x_fsmc.o(.text.FSMC_PCCARDDeInit) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NORSRAMInit) refers to stm32f10x_fsmc.o(.text.FSMC_NORSRAMInit) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NANDInit) refers to stm32f10x_fsmc.o(.text.FSMC_NANDInit) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_PCCARDInit) refers to stm32f10x_fsmc.o(.text.FSMC_PCCARDInit) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NORSRAMStructInit) refers to stm32f10x_fsmc.o(.text.FSMC_NORSRAMStructInit) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NANDStructInit) refers to stm32f10x_fsmc.o(.text.FSMC_NANDStructInit) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_PCCARDStructInit) refers to stm32f10x_fsmc.o(.text.FSMC_PCCARDStructInit) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NORSRAMCmd) refers to stm32f10x_fsmc.o(.text.FSMC_NORSRAMCmd) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NANDCmd) refers to stm32f10x_fsmc.o(.text.FSMC_NANDCmd) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_PCCARDCmd) refers to stm32f10x_fsmc.o(.text.FSMC_PCCARDCmd) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NANDECCCmd) refers to stm32f10x_fsmc.o(.text.FSMC_NANDECCCmd) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_GetECC) refers to stm32f10x_fsmc.o(.text.FSMC_GetECC) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_ITConfig) refers to stm32f10x_fsmc.o(.text.FSMC_ITConfig) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_GetFlagStatus) refers to stm32f10x_fsmc.o(.text.FSMC_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_ClearFlag) refers to stm32f10x_fsmc.o(.text.FSMC_ClearFlag) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_GetITStatus) refers to stm32f10x_fsmc.o(.text.FSMC_GetITStatus) for [Anonymous Symbol]
    stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_ClearITPendingBit) refers to stm32f10x_fsmc.o(.text.FSMC_ClearITPendingBit) for [Anonymous Symbol]
    stm32f10x_gpio.o(.text.GPIO_DeInit) refers to stm32f10x_gpio.o(.rodata..Lswitch.table.GPIO_DeInit.1) for .Lswitch.table.GPIO_DeInit.1
    stm32f10x_gpio.o(.text.GPIO_DeInit) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_DeInit) refers to stm32f10x_gpio.o(.text.GPIO_DeInit) for [Anonymous Symbol]
    stm32f10x_gpio.o(.text.GPIO_AFIODeInit) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_AFIODeInit) refers to stm32f10x_gpio.o(.text.GPIO_AFIODeInit) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_Init) refers to stm32f10x_gpio.o(.text.GPIO_Init) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_StructInit) refers to stm32f10x_gpio.o(.text.GPIO_StructInit) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_ReadInputDataBit) refers to stm32f10x_gpio.o(.text.GPIO_ReadInputDataBit) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_ReadInputData) refers to stm32f10x_gpio.o(.text.GPIO_ReadInputData) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_ReadOutputDataBit) refers to stm32f10x_gpio.o(.text.GPIO_ReadOutputDataBit) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_ReadOutputData) refers to stm32f10x_gpio.o(.text.GPIO_ReadOutputData) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_SetBits) refers to stm32f10x_gpio.o(.text.GPIO_SetBits) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_ResetBits) refers to stm32f10x_gpio.o(.text.GPIO_ResetBits) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_WriteBit) refers to stm32f10x_gpio.o(.text.GPIO_WriteBit) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_Write) refers to stm32f10x_gpio.o(.text.GPIO_Write) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_PinLockConfig) refers to stm32f10x_gpio.o(.text.GPIO_PinLockConfig) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_EventOutputConfig) refers to stm32f10x_gpio.o(.text.GPIO_EventOutputConfig) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_EventOutputCmd) refers to stm32f10x_gpio.o(.text.GPIO_EventOutputCmd) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_PinRemapConfig) refers to stm32f10x_gpio.o(.text.GPIO_PinRemapConfig) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_EXTILineConfig) refers to stm32f10x_gpio.o(.text.GPIO_EXTILineConfig) for [Anonymous Symbol]
    stm32f10x_gpio.o(.ARM.exidx.text.GPIO_ETH_MediaInterfaceConfig) refers to stm32f10x_gpio.o(.text.GPIO_ETH_MediaInterfaceConfig) for [Anonymous Symbol]
    stm32f10x_i2c.o(.text.I2C_DeInit) refers to stm32f10x_rcc.o(.text.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_DeInit) refers to stm32f10x_i2c.o(.text.I2C_DeInit) for [Anonymous Symbol]
    stm32f10x_i2c.o(.text.I2C_Init) refers to stm32f10x_rcc.o(.text.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_Init) refers to stm32f10x_i2c.o(.text.I2C_Init) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_StructInit) refers to stm32f10x_i2c.o(.text.I2C_StructInit) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_Cmd) refers to stm32f10x_i2c.o(.text.I2C_Cmd) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_DMACmd) refers to stm32f10x_i2c.o(.text.I2C_DMACmd) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_DMALastTransferCmd) refers to stm32f10x_i2c.o(.text.I2C_DMALastTransferCmd) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_GenerateSTART) refers to stm32f10x_i2c.o(.text.I2C_GenerateSTART) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_GenerateSTOP) refers to stm32f10x_i2c.o(.text.I2C_GenerateSTOP) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_AcknowledgeConfig) refers to stm32f10x_i2c.o(.text.I2C_AcknowledgeConfig) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_OwnAddress2Config) refers to stm32f10x_i2c.o(.text.I2C_OwnAddress2Config) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_DualAddressCmd) refers to stm32f10x_i2c.o(.text.I2C_DualAddressCmd) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_GeneralCallCmd) refers to stm32f10x_i2c.o(.text.I2C_GeneralCallCmd) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_ITConfig) refers to stm32f10x_i2c.o(.text.I2C_ITConfig) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_SendData) refers to stm32f10x_i2c.o(.text.I2C_SendData) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_ReceiveData) refers to stm32f10x_i2c.o(.text.I2C_ReceiveData) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_Send7bitAddress) refers to stm32f10x_i2c.o(.text.I2C_Send7bitAddress) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_ReadRegister) refers to stm32f10x_i2c.o(.text.I2C_ReadRegister) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_SoftwareResetCmd) refers to stm32f10x_i2c.o(.text.I2C_SoftwareResetCmd) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_NACKPositionConfig) refers to stm32f10x_i2c.o(.text.I2C_NACKPositionConfig) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_SMBusAlertConfig) refers to stm32f10x_i2c.o(.text.I2C_SMBusAlertConfig) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_TransmitPEC) refers to stm32f10x_i2c.o(.text.I2C_TransmitPEC) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_PECPositionConfig) refers to stm32f10x_i2c.o(.text.I2C_PECPositionConfig) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_CalculatePEC) refers to stm32f10x_i2c.o(.text.I2C_CalculatePEC) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_GetPEC) refers to stm32f10x_i2c.o(.text.I2C_GetPEC) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_ARPCmd) refers to stm32f10x_i2c.o(.text.I2C_ARPCmd) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_StretchClockCmd) refers to stm32f10x_i2c.o(.text.I2C_StretchClockCmd) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_FastModeDutyCycleConfig) refers to stm32f10x_i2c.o(.text.I2C_FastModeDutyCycleConfig) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_CheckEvent) refers to stm32f10x_i2c.o(.text.I2C_CheckEvent) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_GetLastEvent) refers to stm32f10x_i2c.o(.text.I2C_GetLastEvent) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_GetFlagStatus) refers to stm32f10x_i2c.o(.text.I2C_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_ClearFlag) refers to stm32f10x_i2c.o(.text.I2C_ClearFlag) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_GetITStatus) refers to stm32f10x_i2c.o(.text.I2C_GetITStatus) for [Anonymous Symbol]
    stm32f10x_i2c.o(.ARM.exidx.text.I2C_ClearITPendingBit) refers to stm32f10x_i2c.o(.text.I2C_ClearITPendingBit) for [Anonymous Symbol]
    stm32f10x_iwdg.o(.ARM.exidx.text.IWDG_WriteAccessCmd) refers to stm32f10x_iwdg.o(.text.IWDG_WriteAccessCmd) for [Anonymous Symbol]
    stm32f10x_iwdg.o(.ARM.exidx.text.IWDG_SetPrescaler) refers to stm32f10x_iwdg.o(.text.IWDG_SetPrescaler) for [Anonymous Symbol]
    stm32f10x_iwdg.o(.ARM.exidx.text.IWDG_SetReload) refers to stm32f10x_iwdg.o(.text.IWDG_SetReload) for [Anonymous Symbol]
    stm32f10x_iwdg.o(.ARM.exidx.text.IWDG_ReloadCounter) refers to stm32f10x_iwdg.o(.text.IWDG_ReloadCounter) for [Anonymous Symbol]
    stm32f10x_iwdg.o(.ARM.exidx.text.IWDG_Enable) refers to stm32f10x_iwdg.o(.text.IWDG_Enable) for [Anonymous Symbol]
    stm32f10x_iwdg.o(.ARM.exidx.text.IWDG_GetFlagStatus) refers to stm32f10x_iwdg.o(.text.IWDG_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_pwr.o(.text.PWR_DeInit) refers to stm32f10x_rcc.o(.text.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_pwr.o(.ARM.exidx.text.PWR_DeInit) refers to stm32f10x_pwr.o(.text.PWR_DeInit) for [Anonymous Symbol]
    stm32f10x_pwr.o(.ARM.exidx.text.PWR_BackupAccessCmd) refers to stm32f10x_pwr.o(.text.PWR_BackupAccessCmd) for [Anonymous Symbol]
    stm32f10x_pwr.o(.ARM.exidx.text.PWR_PVDCmd) refers to stm32f10x_pwr.o(.text.PWR_PVDCmd) for [Anonymous Symbol]
    stm32f10x_pwr.o(.ARM.exidx.text.PWR_PVDLevelConfig) refers to stm32f10x_pwr.o(.text.PWR_PVDLevelConfig) for [Anonymous Symbol]
    stm32f10x_pwr.o(.ARM.exidx.text.PWR_WakeUpPinCmd) refers to stm32f10x_pwr.o(.text.PWR_WakeUpPinCmd) for [Anonymous Symbol]
    stm32f10x_pwr.o(.ARM.exidx.text.PWR_EnterSTOPMode) refers to stm32f10x_pwr.o(.text.PWR_EnterSTOPMode) for [Anonymous Symbol]
    stm32f10x_pwr.o(.ARM.exidx.text.PWR_EnterSTANDBYMode) refers to stm32f10x_pwr.o(.text.PWR_EnterSTANDBYMode) for [Anonymous Symbol]
    stm32f10x_pwr.o(.ARM.exidx.text.PWR_GetFlagStatus) refers to stm32f10x_pwr.o(.text.PWR_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_pwr.o(.ARM.exidx.text.PWR_ClearFlag) refers to stm32f10x_pwr.o(.text.PWR_ClearFlag) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_DeInit) refers to stm32f10x_rcc.o(.text.RCC_DeInit) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_HSEConfig) refers to stm32f10x_rcc.o(.text.RCC_HSEConfig) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_WaitForHSEStartUp) refers to stm32f10x_rcc.o(.text.RCC_WaitForHSEStartUp) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_GetFlagStatus) refers to stm32f10x_rcc.o(.text.RCC_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_AdjustHSICalibrationValue) refers to stm32f10x_rcc.o(.text.RCC_AdjustHSICalibrationValue) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_HSICmd) refers to stm32f10x_rcc.o(.text.RCC_HSICmd) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_PLLConfig) refers to stm32f10x_rcc.o(.text.RCC_PLLConfig) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_PLLCmd) refers to stm32f10x_rcc.o(.text.RCC_PLLCmd) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_SYSCLKConfig) refers to stm32f10x_rcc.o(.text.RCC_SYSCLKConfig) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_GetSYSCLKSource) refers to stm32f10x_rcc.o(.text.RCC_GetSYSCLKSource) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_HCLKConfig) refers to stm32f10x_rcc.o(.text.RCC_HCLKConfig) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_PCLK1Config) refers to stm32f10x_rcc.o(.text.RCC_PCLK1Config) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_PCLK2Config) refers to stm32f10x_rcc.o(.text.RCC_PCLK2Config) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_ITConfig) refers to stm32f10x_rcc.o(.text.RCC_ITConfig) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_USBCLKConfig) refers to stm32f10x_rcc.o(.text.RCC_USBCLKConfig) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_ADCCLKConfig) refers to stm32f10x_rcc.o(.text.RCC_ADCCLKConfig) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_LSEConfig) refers to stm32f10x_rcc.o(.text.RCC_LSEConfig) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_LSICmd) refers to stm32f10x_rcc.o(.text.RCC_LSICmd) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_RTCCLKConfig) refers to stm32f10x_rcc.o(.text.RCC_RTCCLKConfig) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_RTCCLKCmd) refers to stm32f10x_rcc.o(.text.RCC_RTCCLKCmd) for [Anonymous Symbol]
    stm32f10x_rcc.o(.text.RCC_GetClocksFreq) refers to stm32f10x_rcc.o(.rodata.APBAHBPrescTable) for APBAHBPrescTable
    stm32f10x_rcc.o(.text.RCC_GetClocksFreq) refers to stm32f10x_rcc.o(.rodata.ADCPrescTable) for ADCPrescTable
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_GetClocksFreq) refers to stm32f10x_rcc.o(.text.RCC_GetClocksFreq) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_AHBPeriphClockCmd) refers to stm32f10x_rcc.o(.text.RCC_AHBPeriphClockCmd) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_APB2PeriphClockCmd) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphClockCmd) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_APB1PeriphClockCmd) refers to stm32f10x_rcc.o(.text.RCC_APB1PeriphClockCmd) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_APB2PeriphResetCmd) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphResetCmd) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_APB1PeriphResetCmd) refers to stm32f10x_rcc.o(.text.RCC_APB1PeriphResetCmd) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_BackupResetCmd) refers to stm32f10x_rcc.o(.text.RCC_BackupResetCmd) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_ClockSecuritySystemCmd) refers to stm32f10x_rcc.o(.text.RCC_ClockSecuritySystemCmd) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_MCOConfig) refers to stm32f10x_rcc.o(.text.RCC_MCOConfig) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_ClearFlag) refers to stm32f10x_rcc.o(.text.RCC_ClearFlag) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_GetITStatus) refers to stm32f10x_rcc.o(.text.RCC_GetITStatus) for [Anonymous Symbol]
    stm32f10x_rcc.o(.ARM.exidx.text.RCC_ClearITPendingBit) refers to stm32f10x_rcc.o(.text.RCC_ClearITPendingBit) for [Anonymous Symbol]
    stm32f10x_rtc.o(.ARM.exidx.text.RTC_ITConfig) refers to stm32f10x_rtc.o(.text.RTC_ITConfig) for [Anonymous Symbol]
    stm32f10x_rtc.o(.ARM.exidx.text.RTC_EnterConfigMode) refers to stm32f10x_rtc.o(.text.RTC_EnterConfigMode) for [Anonymous Symbol]
    stm32f10x_rtc.o(.ARM.exidx.text.RTC_ExitConfigMode) refers to stm32f10x_rtc.o(.text.RTC_ExitConfigMode) for [Anonymous Symbol]
    stm32f10x_rtc.o(.ARM.exidx.text.RTC_GetCounter) refers to stm32f10x_rtc.o(.text.RTC_GetCounter) for [Anonymous Symbol]
    stm32f10x_rtc.o(.ARM.exidx.text.RTC_SetCounter) refers to stm32f10x_rtc.o(.text.RTC_SetCounter) for [Anonymous Symbol]
    stm32f10x_rtc.o(.ARM.exidx.text.RTC_SetPrescaler) refers to stm32f10x_rtc.o(.text.RTC_SetPrescaler) for [Anonymous Symbol]
    stm32f10x_rtc.o(.ARM.exidx.text.RTC_SetAlarm) refers to stm32f10x_rtc.o(.text.RTC_SetAlarm) for [Anonymous Symbol]
    stm32f10x_rtc.o(.ARM.exidx.text.RTC_GetDivider) refers to stm32f10x_rtc.o(.text.RTC_GetDivider) for [Anonymous Symbol]
    stm32f10x_rtc.o(.ARM.exidx.text.RTC_WaitForLastTask) refers to stm32f10x_rtc.o(.text.RTC_WaitForLastTask) for [Anonymous Symbol]
    stm32f10x_rtc.o(.ARM.exidx.text.RTC_WaitForSynchro) refers to stm32f10x_rtc.o(.text.RTC_WaitForSynchro) for [Anonymous Symbol]
    stm32f10x_rtc.o(.ARM.exidx.text.RTC_GetFlagStatus) refers to stm32f10x_rtc.o(.text.RTC_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_rtc.o(.ARM.exidx.text.RTC_ClearFlag) refers to stm32f10x_rtc.o(.text.RTC_ClearFlag) for [Anonymous Symbol]
    stm32f10x_rtc.o(.ARM.exidx.text.RTC_GetITStatus) refers to stm32f10x_rtc.o(.text.RTC_GetITStatus) for [Anonymous Symbol]
    stm32f10x_rtc.o(.ARM.exidx.text.RTC_ClearITPendingBit) refers to stm32f10x_rtc.o(.text.RTC_ClearITPendingBit) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_DeInit) refers to stm32f10x_sdio.o(.text.SDIO_DeInit) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_Init) refers to stm32f10x_sdio.o(.text.SDIO_Init) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_StructInit) refers to stm32f10x_sdio.o(.text.SDIO_StructInit) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_ClockCmd) refers to stm32f10x_sdio.o(.text.SDIO_ClockCmd) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_SetPowerState) refers to stm32f10x_sdio.o(.text.SDIO_SetPowerState) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_GetPowerState) refers to stm32f10x_sdio.o(.text.SDIO_GetPowerState) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_ITConfig) refers to stm32f10x_sdio.o(.text.SDIO_ITConfig) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_DMACmd) refers to stm32f10x_sdio.o(.text.SDIO_DMACmd) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_SendCommand) refers to stm32f10x_sdio.o(.text.SDIO_SendCommand) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_CmdStructInit) refers to stm32f10x_sdio.o(.text.SDIO_CmdStructInit) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_GetCommandResponse) refers to stm32f10x_sdio.o(.text.SDIO_GetCommandResponse) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_GetResponse) refers to stm32f10x_sdio.o(.text.SDIO_GetResponse) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_DataConfig) refers to stm32f10x_sdio.o(.text.SDIO_DataConfig) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_DataStructInit) refers to stm32f10x_sdio.o(.text.SDIO_DataStructInit) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_GetDataCounter) refers to stm32f10x_sdio.o(.text.SDIO_GetDataCounter) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_ReadData) refers to stm32f10x_sdio.o(.text.SDIO_ReadData) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_WriteData) refers to stm32f10x_sdio.o(.text.SDIO_WriteData) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_GetFIFOCount) refers to stm32f10x_sdio.o(.text.SDIO_GetFIFOCount) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_StartSDIOReadWait) refers to stm32f10x_sdio.o(.text.SDIO_StartSDIOReadWait) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_StopSDIOReadWait) refers to stm32f10x_sdio.o(.text.SDIO_StopSDIOReadWait) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_SetSDIOReadWaitMode) refers to stm32f10x_sdio.o(.text.SDIO_SetSDIOReadWaitMode) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_SetSDIOOperation) refers to stm32f10x_sdio.o(.text.SDIO_SetSDIOOperation) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_SendSDIOSuspendCmd) refers to stm32f10x_sdio.o(.text.SDIO_SendSDIOSuspendCmd) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_CommandCompletionCmd) refers to stm32f10x_sdio.o(.text.SDIO_CommandCompletionCmd) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_CEATAITCmd) refers to stm32f10x_sdio.o(.text.SDIO_CEATAITCmd) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_SendCEATACmd) refers to stm32f10x_sdio.o(.text.SDIO_SendCEATACmd) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_GetFlagStatus) refers to stm32f10x_sdio.o(.text.SDIO_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_ClearFlag) refers to stm32f10x_sdio.o(.text.SDIO_ClearFlag) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_GetITStatus) refers to stm32f10x_sdio.o(.text.SDIO_GetITStatus) for [Anonymous Symbol]
    stm32f10x_sdio.o(.ARM.exidx.text.SDIO_ClearITPendingBit) refers to stm32f10x_sdio.o(.text.SDIO_ClearITPendingBit) for [Anonymous Symbol]
    stm32f10x_spi.o(.text.SPI_I2S_DeInit) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_spi.o(.text.SPI_I2S_DeInit) refers to stm32f10x_rcc.o(.text.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_DeInit) refers to stm32f10x_spi.o(.text.SPI_I2S_DeInit) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_Init) refers to stm32f10x_spi.o(.text.SPI_Init) for [Anonymous Symbol]
    stm32f10x_spi.o(.text.I2S_Init) refers to stm32f10x_rcc.o(.text.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_spi.o(.ARM.exidx.text.I2S_Init) refers to stm32f10x_spi.o(.text.I2S_Init) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_StructInit) refers to stm32f10x_spi.o(.text.SPI_StructInit) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.I2S_StructInit) refers to stm32f10x_spi.o(.text.I2S_StructInit) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_Cmd) refers to stm32f10x_spi.o(.text.SPI_Cmd) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.I2S_Cmd) refers to stm32f10x_spi.o(.text.I2S_Cmd) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_ITConfig) refers to stm32f10x_spi.o(.text.SPI_I2S_ITConfig) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_DMACmd) refers to stm32f10x_spi.o(.text.SPI_I2S_DMACmd) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_SendData) refers to stm32f10x_spi.o(.text.SPI_I2S_SendData) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_ReceiveData) refers to stm32f10x_spi.o(.text.SPI_I2S_ReceiveData) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_NSSInternalSoftwareConfig) refers to stm32f10x_spi.o(.text.SPI_NSSInternalSoftwareConfig) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_SSOutputCmd) refers to stm32f10x_spi.o(.text.SPI_SSOutputCmd) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_DataSizeConfig) refers to stm32f10x_spi.o(.text.SPI_DataSizeConfig) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_TransmitCRC) refers to stm32f10x_spi.o(.text.SPI_TransmitCRC) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_CalculateCRC) refers to stm32f10x_spi.o(.text.SPI_CalculateCRC) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_GetCRC) refers to stm32f10x_spi.o(.text.SPI_GetCRC) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_GetCRCPolynomial) refers to stm32f10x_spi.o(.text.SPI_GetCRCPolynomial) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_BiDirectionalLineConfig) refers to stm32f10x_spi.o(.text.SPI_BiDirectionalLineConfig) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_GetFlagStatus) refers to stm32f10x_spi.o(.text.SPI_I2S_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_ClearFlag) refers to stm32f10x_spi.o(.text.SPI_I2S_ClearFlag) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_GetITStatus) refers to stm32f10x_spi.o(.text.SPI_I2S_GetITStatus) for [Anonymous Symbol]
    stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_ClearITPendingBit) refers to stm32f10x_spi.o(.text.SPI_I2S_ClearITPendingBit) for [Anonymous Symbol]
    stm32f10x_tim.o(.text.TIM_DeInit) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_tim.o(.text.TIM_DeInit) refers to stm32f10x_rcc.o(.text.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_tim.o(.ARM.exidx.text.TIM_DeInit) refers to stm32f10x_tim.o(.text.TIM_DeInit) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_TimeBaseInit) refers to stm32f10x_tim.o(.text.TIM_TimeBaseInit) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC1Init) refers to stm32f10x_tim.o(.text.TIM_OC1Init) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC2Init) refers to stm32f10x_tim.o(.text.TIM_OC2Init) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC3Init) refers to stm32f10x_tim.o(.text.TIM_OC3Init) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC4Init) refers to stm32f10x_tim.o(.text.TIM_OC4Init) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ICInit) refers to stm32f10x_tim.o(.text.TIM_ICInit) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SetIC1Prescaler) refers to stm32f10x_tim.o(.text.TIM_SetIC1Prescaler) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SetIC2Prescaler) refers to stm32f10x_tim.o(.text.TIM_SetIC2Prescaler) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SetIC3Prescaler) refers to stm32f10x_tim.o(.text.TIM_SetIC3Prescaler) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SetIC4Prescaler) refers to stm32f10x_tim.o(.text.TIM_SetIC4Prescaler) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_PWMIConfig) refers to stm32f10x_tim.o(.text.TIM_PWMIConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_BDTRConfig) refers to stm32f10x_tim.o(.text.TIM_BDTRConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_TimeBaseStructInit) refers to stm32f10x_tim.o(.text.TIM_TimeBaseStructInit) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OCStructInit) refers to stm32f10x_tim.o(.text.TIM_OCStructInit) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ICStructInit) refers to stm32f10x_tim.o(.text.TIM_ICStructInit) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_BDTRStructInit) refers to stm32f10x_tim.o(.text.TIM_BDTRStructInit) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_Cmd) refers to stm32f10x_tim.o(.text.TIM_Cmd) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_CtrlPWMOutputs) refers to stm32f10x_tim.o(.text.TIM_CtrlPWMOutputs) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ITConfig) refers to stm32f10x_tim.o(.text.TIM_ITConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_GenerateEvent) refers to stm32f10x_tim.o(.text.TIM_GenerateEvent) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_DMAConfig) refers to stm32f10x_tim.o(.text.TIM_DMAConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_DMACmd) refers to stm32f10x_tim.o(.text.TIM_DMACmd) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_InternalClockConfig) refers to stm32f10x_tim.o(.text.TIM_InternalClockConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ITRxExternalClockConfig) refers to stm32f10x_tim.o(.text.TIM_ITRxExternalClockConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectInputTrigger) refers to stm32f10x_tim.o(.text.TIM_SelectInputTrigger) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_TIxExternalClockConfig) refers to stm32f10x_tim.o(.text.TIM_TIxExternalClockConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ETRClockMode1Config) refers to stm32f10x_tim.o(.text.TIM_ETRClockMode1Config) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ETRConfig) refers to stm32f10x_tim.o(.text.TIM_ETRConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ETRClockMode2Config) refers to stm32f10x_tim.o(.text.TIM_ETRClockMode2Config) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_PrescalerConfig) refers to stm32f10x_tim.o(.text.TIM_PrescalerConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_CounterModeConfig) refers to stm32f10x_tim.o(.text.TIM_CounterModeConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_EncoderInterfaceConfig) refers to stm32f10x_tim.o(.text.TIM_EncoderInterfaceConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ForcedOC1Config) refers to stm32f10x_tim.o(.text.TIM_ForcedOC1Config) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ForcedOC2Config) refers to stm32f10x_tim.o(.text.TIM_ForcedOC2Config) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ForcedOC3Config) refers to stm32f10x_tim.o(.text.TIM_ForcedOC3Config) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ForcedOC4Config) refers to stm32f10x_tim.o(.text.TIM_ForcedOC4Config) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ARRPreloadConfig) refers to stm32f10x_tim.o(.text.TIM_ARRPreloadConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectCOM) refers to stm32f10x_tim.o(.text.TIM_SelectCOM) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectCCDMA) refers to stm32f10x_tim.o(.text.TIM_SelectCCDMA) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_CCPreloadControl) refers to stm32f10x_tim.o(.text.TIM_CCPreloadControl) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC1PreloadConfig) refers to stm32f10x_tim.o(.text.TIM_OC1PreloadConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC2PreloadConfig) refers to stm32f10x_tim.o(.text.TIM_OC2PreloadConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC3PreloadConfig) refers to stm32f10x_tim.o(.text.TIM_OC3PreloadConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC4PreloadConfig) refers to stm32f10x_tim.o(.text.TIM_OC4PreloadConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC1FastConfig) refers to stm32f10x_tim.o(.text.TIM_OC1FastConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC2FastConfig) refers to stm32f10x_tim.o(.text.TIM_OC2FastConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC3FastConfig) refers to stm32f10x_tim.o(.text.TIM_OC3FastConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC4FastConfig) refers to stm32f10x_tim.o(.text.TIM_OC4FastConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ClearOC1Ref) refers to stm32f10x_tim.o(.text.TIM_ClearOC1Ref) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ClearOC2Ref) refers to stm32f10x_tim.o(.text.TIM_ClearOC2Ref) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ClearOC3Ref) refers to stm32f10x_tim.o(.text.TIM_ClearOC3Ref) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ClearOC4Ref) refers to stm32f10x_tim.o(.text.TIM_ClearOC4Ref) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC1PolarityConfig) refers to stm32f10x_tim.o(.text.TIM_OC1PolarityConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC1NPolarityConfig) refers to stm32f10x_tim.o(.text.TIM_OC1NPolarityConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC2PolarityConfig) refers to stm32f10x_tim.o(.text.TIM_OC2PolarityConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC2NPolarityConfig) refers to stm32f10x_tim.o(.text.TIM_OC2NPolarityConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC3PolarityConfig) refers to stm32f10x_tim.o(.text.TIM_OC3PolarityConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC3NPolarityConfig) refers to stm32f10x_tim.o(.text.TIM_OC3NPolarityConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_OC4PolarityConfig) refers to stm32f10x_tim.o(.text.TIM_OC4PolarityConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_CCxCmd) refers to stm32f10x_tim.o(.text.TIM_CCxCmd) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_CCxNCmd) refers to stm32f10x_tim.o(.text.TIM_CCxNCmd) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectOCxM) refers to stm32f10x_tim.o(.text.TIM_SelectOCxM) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_UpdateDisableConfig) refers to stm32f10x_tim.o(.text.TIM_UpdateDisableConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_UpdateRequestConfig) refers to stm32f10x_tim.o(.text.TIM_UpdateRequestConfig) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectHallSensor) refers to stm32f10x_tim.o(.text.TIM_SelectHallSensor) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectOnePulseMode) refers to stm32f10x_tim.o(.text.TIM_SelectOnePulseMode) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectOutputTrigger) refers to stm32f10x_tim.o(.text.TIM_SelectOutputTrigger) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectSlaveMode) refers to stm32f10x_tim.o(.text.TIM_SelectSlaveMode) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectMasterSlaveMode) refers to stm32f10x_tim.o(.text.TIM_SelectMasterSlaveMode) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SetCounter) refers to stm32f10x_tim.o(.text.TIM_SetCounter) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SetAutoreload) refers to stm32f10x_tim.o(.text.TIM_SetAutoreload) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SetCompare1) refers to stm32f10x_tim.o(.text.TIM_SetCompare1) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SetCompare2) refers to stm32f10x_tim.o(.text.TIM_SetCompare2) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SetCompare3) refers to stm32f10x_tim.o(.text.TIM_SetCompare3) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SetCompare4) refers to stm32f10x_tim.o(.text.TIM_SetCompare4) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_SetClockDivision) refers to stm32f10x_tim.o(.text.TIM_SetClockDivision) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_GetCapture1) refers to stm32f10x_tim.o(.text.TIM_GetCapture1) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_GetCapture2) refers to stm32f10x_tim.o(.text.TIM_GetCapture2) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_GetCapture3) refers to stm32f10x_tim.o(.text.TIM_GetCapture3) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_GetCapture4) refers to stm32f10x_tim.o(.text.TIM_GetCapture4) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_GetCounter) refers to stm32f10x_tim.o(.text.TIM_GetCounter) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_GetPrescaler) refers to stm32f10x_tim.o(.text.TIM_GetPrescaler) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_GetFlagStatus) refers to stm32f10x_tim.o(.text.TIM_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ClearFlag) refers to stm32f10x_tim.o(.text.TIM_ClearFlag) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_GetITStatus) refers to stm32f10x_tim.o(.text.TIM_GetITStatus) for [Anonymous Symbol]
    stm32f10x_tim.o(.ARM.exidx.text.TIM_ClearITPendingBit) refers to stm32f10x_tim.o(.text.TIM_ClearITPendingBit) for [Anonymous Symbol]
    stm32f10x_usart.o(.text.USART_DeInit) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f10x_usart.o(.text.USART_DeInit) refers to stm32f10x_rcc.o(.text.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_usart.o(.ARM.exidx.text.USART_DeInit) refers to stm32f10x_usart.o(.text.USART_DeInit) for [Anonymous Symbol]
    stm32f10x_usart.o(.text.USART_Init) refers to stm32f10x_rcc.o(.text.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f10x_usart.o(.ARM.exidx.text.USART_Init) refers to stm32f10x_usart.o(.text.USART_Init) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_StructInit) refers to stm32f10x_usart.o(.text.USART_StructInit) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_ClockInit) refers to stm32f10x_usart.o(.text.USART_ClockInit) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_ClockStructInit) refers to stm32f10x_usart.o(.text.USART_ClockStructInit) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_Cmd) refers to stm32f10x_usart.o(.text.USART_Cmd) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_ITConfig) refers to stm32f10x_usart.o(.text.USART_ITConfig) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_DMACmd) refers to stm32f10x_usart.o(.text.USART_DMACmd) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_SetAddress) refers to stm32f10x_usart.o(.text.USART_SetAddress) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_WakeUpConfig) refers to stm32f10x_usart.o(.text.USART_WakeUpConfig) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_ReceiverWakeUpCmd) refers to stm32f10x_usart.o(.text.USART_ReceiverWakeUpCmd) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_LINBreakDetectLengthConfig) refers to stm32f10x_usart.o(.text.USART_LINBreakDetectLengthConfig) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_LINCmd) refers to stm32f10x_usart.o(.text.USART_LINCmd) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_SendData) refers to stm32f10x_usart.o(.text.USART_SendData) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_ReceiveData) refers to stm32f10x_usart.o(.text.USART_ReceiveData) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_SendBreak) refers to stm32f10x_usart.o(.text.USART_SendBreak) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_SetGuardTime) refers to stm32f10x_usart.o(.text.USART_SetGuardTime) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_SetPrescaler) refers to stm32f10x_usart.o(.text.USART_SetPrescaler) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_SmartCardCmd) refers to stm32f10x_usart.o(.text.USART_SmartCardCmd) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_SmartCardNACKCmd) refers to stm32f10x_usart.o(.text.USART_SmartCardNACKCmd) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_HalfDuplexCmd) refers to stm32f10x_usart.o(.text.USART_HalfDuplexCmd) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_OverSampling8Cmd) refers to stm32f10x_usart.o(.text.USART_OverSampling8Cmd) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_OneBitMethodCmd) refers to stm32f10x_usart.o(.text.USART_OneBitMethodCmd) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_IrDAConfig) refers to stm32f10x_usart.o(.text.USART_IrDAConfig) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_IrDACmd) refers to stm32f10x_usart.o(.text.USART_IrDACmd) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_GetFlagStatus) refers to stm32f10x_usart.o(.text.USART_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_ClearFlag) refers to stm32f10x_usart.o(.text.USART_ClearFlag) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_GetITStatus) refers to stm32f10x_usart.o(.text.USART_GetITStatus) for [Anonymous Symbol]
    stm32f10x_usart.o(.ARM.exidx.text.USART_ClearITPendingBit) refers to stm32f10x_usart.o(.text.USART_ClearITPendingBit) for [Anonymous Symbol]
    stm32f10x_wwdg.o(.text.WWDG_DeInit) refers to stm32f10x_rcc.o(.text.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f10x_wwdg.o(.ARM.exidx.text.WWDG_DeInit) refers to stm32f10x_wwdg.o(.text.WWDG_DeInit) for [Anonymous Symbol]
    stm32f10x_wwdg.o(.ARM.exidx.text.WWDG_SetPrescaler) refers to stm32f10x_wwdg.o(.text.WWDG_SetPrescaler) for [Anonymous Symbol]
    stm32f10x_wwdg.o(.ARM.exidx.text.WWDG_SetWindowValue) refers to stm32f10x_wwdg.o(.text.WWDG_SetWindowValue) for [Anonymous Symbol]
    stm32f10x_wwdg.o(.ARM.exidx.text.WWDG_EnableIT) refers to stm32f10x_wwdg.o(.text.WWDG_EnableIT) for [Anonymous Symbol]
    stm32f10x_wwdg.o(.ARM.exidx.text.WWDG_SetCounter) refers to stm32f10x_wwdg.o(.text.WWDG_SetCounter) for [Anonymous Symbol]
    stm32f10x_wwdg.o(.ARM.exidx.text.WWDG_Enable) refers to stm32f10x_wwdg.o(.text.WWDG_Enable) for [Anonymous Symbol]
    stm32f10x_wwdg.o(.ARM.exidx.text.WWDG_GetFlagStatus) refers to stm32f10x_wwdg.o(.text.WWDG_GetFlagStatus) for [Anonymous Symbol]
    stm32f10x_wwdg.o(.ARM.exidx.text.WWDG_ClearFlag) refers to stm32f10x_wwdg.o(.text.WWDG_ClearFlag) for [Anonymous Symbol]
    startup_stm32f10x_md.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_md.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_md.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_md.o(RESET) refers to startup_stm32f10x_md.o(STACK) for __initial_sp
    startup_stm32f10x_md.o(RESET) refers to startup_stm32f10x_md.o(.text) for Reset_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(.text.NMI_Handler) for NMI_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(.text.HardFault_Handler) for HardFault_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(.text.MemManage_Handler) for MemManage_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(.text.BusFault_Handler) for BusFault_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(.text.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(.text.SVC_Handler) for SVC_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(.text.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(.text.PendSV_Handler) for PendSV_Handler
    startup_stm32f10x_md.o(RESET) refers to stm32f10x_it.o(.text.SysTick_Handler) for SysTick_Handler
    startup_stm32f10x_md.o(RESET) refers to encoder.o(.text.EXTI0_IRQHandler) for EXTI0_IRQHandler
    startup_stm32f10x_md.o(RESET) refers to encoder.o(.text.EXTI2_IRQHandler) for EXTI2_IRQHandler
    startup_stm32f10x_md.o(RESET) refers to encoder.o(.text.TIM2_IRQHandler) for TIM2_IRQHandler
    startup_stm32f10x_md.o(RESET) refers to timer.o(.text.TIM3_IRQHandler) for TIM3_IRQHandler
    startup_stm32f10x_md.o(RESET) refers to usart.o(.text.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f10x_md.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_md.o(.text) refers to system_stm32f10x.o(.text.SystemInit) for SystemInit
    startup_stm32f10x_md.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f10x_md.o(.text) refers to startup_stm32f10x_md.o(HEAP) for Heap_Mem
    startup_stm32f10x_md.o(.text) refers to startup_stm32f10x_md.o(STACK) for Stack_Mem
    system_stm32f10x.o(.ARM.exidx.text.SystemInit) refers to system_stm32f10x.o(.text.SystemInit) for [Anonymous Symbol]
    system_stm32f10x.o(.text.SystemCoreClockUpdate) refers to system_stm32f10x.o(.data.SystemCoreClock) for SystemCoreClock
    system_stm32f10x.o(.text.SystemCoreClockUpdate) refers to system_stm32f10x.o(.rodata.AHBPrescTable) for AHBPrescTable
    system_stm32f10x.o(.ARM.exidx.text.SystemCoreClockUpdate) refers to system_stm32f10x.o(.text.SystemCoreClockUpdate) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__get_PSP) refers to core_cm3.o(.text.__get_PSP) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__set_PSP) refers to core_cm3.o(.text.__set_PSP) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__get_MSP) refers to core_cm3.o(.text.__get_MSP) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__set_MSP) refers to core_cm3.o(.text.__set_MSP) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__get_BASEPRI) refers to core_cm3.o(.text.__get_BASEPRI) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__set_BASEPRI) refers to core_cm3.o(.text.__set_BASEPRI) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__get_PRIMASK) refers to core_cm3.o(.text.__get_PRIMASK) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__set_PRIMASK) refers to core_cm3.o(.text.__set_PRIMASK) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__get_FAULTMASK) refers to core_cm3.o(.text.__get_FAULTMASK) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__set_FAULTMASK) refers to core_cm3.o(.text.__set_FAULTMASK) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__get_CONTROL) refers to core_cm3.o(.text.__get_CONTROL) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__set_CONTROL) refers to core_cm3.o(.text.__set_CONTROL) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__REV) refers to core_cm3.o(.text.__REV) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__REV16) refers to core_cm3.o(.text.__REV16) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__REVSH) refers to core_cm3.o(.text.__REVSH) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__RBIT) refers to core_cm3.o(.text.__RBIT) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__LDREXB) refers to core_cm3.o(.text.__LDREXB) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__LDREXH) refers to core_cm3.o(.text.__LDREXH) for [Anonymous Symbol]
    core_cm3.o(.ARM.exidx.text.__LDREXW) refers to core_cm3.o(.text.__LDREXW) for [Anonymous Symbol]
    main.o(.text.main) refers to system_stm32f10x.o(.text.SystemInit) for SystemInit
    main.o(.text.main) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    main.o(.text.main) refers to stm32f10x_gpio.o(.text.GPIO_Init) for GPIO_Init
    main.o(.text.main) refers to gpio_driver.o(.text.GPIO_SetPin) for GPIO_SetPin
    main.o(.text.main) refers to pwm.o(.text.PWM_Init) for PWM_Init
    main.o(.text.main) refers to pwm.o(.text.PWM_Start) for PWM_Start
    main.o(.text.main) refers to encoder.o(.text.Encoder_Init) for Encoder_Init
    main.o(.text.main) refers to oled.o(.text.OLED_Init) for OLED_Init
    main.o(.text.main) refers to main.o(.bss.motor) for motor
    main.o(.text.main) refers to motor_control.o(.text.MotorControl_Init) for MotorControl_Init
    main.o(.text.main) refers to motor_control.o(.text.MotorControl_SetSpeed) for MotorControl_SetSpeed
    main.o(.text.main) refers to timer.o(.text.TIM3_Config) for TIM3_Config
    main.o(.text.main) refers to encoder.o(.text.Show_Encoder) for Show_Encoder
    main.o(.ARM.exidx.text.main) refers to main.o(.text.main) for [Anonymous Symbol]
    stm32f10x_it.o(.ARM.exidx.text.NMI_Handler) refers to stm32f10x_it.o(.text.NMI_Handler) for [Anonymous Symbol]
    stm32f10x_it.o(.ARM.exidx.text.HardFault_Handler) refers to stm32f10x_it.o(.text.HardFault_Handler) for [Anonymous Symbol]
    stm32f10x_it.o(.ARM.exidx.text.MemManage_Handler) refers to stm32f10x_it.o(.text.MemManage_Handler) for [Anonymous Symbol]
    stm32f10x_it.o(.ARM.exidx.text.BusFault_Handler) refers to stm32f10x_it.o(.text.BusFault_Handler) for [Anonymous Symbol]
    stm32f10x_it.o(.ARM.exidx.text.UsageFault_Handler) refers to stm32f10x_it.o(.text.UsageFault_Handler) for [Anonymous Symbol]
    stm32f10x_it.o(.ARM.exidx.text.SVC_Handler) refers to stm32f10x_it.o(.text.SVC_Handler) for [Anonymous Symbol]
    stm32f10x_it.o(.ARM.exidx.text.DebugMon_Handler) refers to stm32f10x_it.o(.text.DebugMon_Handler) for [Anonymous Symbol]
    stm32f10x_it.o(.ARM.exidx.text.PendSV_Handler) refers to stm32f10x_it.o(.text.PendSV_Handler) for [Anonymous Symbol]
    stm32f10x_it.o(.ARM.exidx.text.SysTick_Handler) refers to stm32f10x_it.o(.text.SysTick_Handler) for [Anonymous Symbol]
    gpio_driver.o(.text.GPIO_InitPin) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    gpio_driver.o(.text.GPIO_InitPin) refers to stm32f10x_gpio.o(.text.GPIO_Init) for GPIO_Init
    gpio_driver.o(.ARM.exidx.text.GPIO_InitPin) refers to gpio_driver.o(.text.GPIO_InitPin) for [Anonymous Symbol]
    gpio_driver.o(.text.GPIO_SetPin) refers to stm32f10x_gpio.o(.text.GPIO_WriteBit) for GPIO_WriteBit
    gpio_driver.o(.ARM.exidx.text.GPIO_SetPin) refers to gpio_driver.o(.text.GPIO_SetPin) for [Anonymous Symbol]
    gpio_driver.o(.text.GPIO_GetPin) refers to stm32f10x_gpio.o(.text.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    gpio_driver.o(.ARM.exidx.text.GPIO_GetPin) refers to gpio_driver.o(.text.GPIO_GetPin) for [Anonymous Symbol]
    gpio_driver.o(.text.GPIO_TogglePin) refers to stm32f10x_gpio.o(.text.GPIO_ReadOutputDataBit) for GPIO_ReadOutputDataBit
    gpio_driver.o(.text.GPIO_TogglePin) refers to stm32f10x_gpio.o(.text.GPIO_ResetBits) for GPIO_ResetBits
    gpio_driver.o(.text.GPIO_TogglePin) refers to stm32f10x_gpio.o(.text.GPIO_SetBits) for GPIO_SetBits
    gpio_driver.o(.ARM.exidx.text.GPIO_TogglePin) refers to gpio_driver.o(.text.GPIO_TogglePin) for [Anonymous Symbol]
    pwm.o(.text.PWM_Init) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    pwm.o(.text.PWM_Init) refers to stm32f10x_gpio.o(.text.GPIO_Init) for GPIO_Init
    pwm.o(.text.PWM_Init) refers to system_stm32f10x.o(.data.SystemCoreClock) for SystemCoreClock
    pwm.o(.text.PWM_Init) refers to pwm.o(.bss.pwmPeriod) for pwmPeriod
    pwm.o(.text.PWM_Init) refers to stm32f10x_tim.o(.text.TIM_TimeBaseInit) for TIM_TimeBaseInit
    pwm.o(.text.PWM_Init) refers to stm32f10x_tim.o(.text.TIM_OC3Init) for TIM_OC3Init
    pwm.o(.text.PWM_Init) refers to stm32f10x_tim.o(.text.TIM_OC3PreloadConfig) for TIM_OC3PreloadConfig
    pwm.o(.text.PWM_Init) refers to stm32f10x_tim.o(.text.TIM_OC1Init) for TIM_OC1Init
    pwm.o(.text.PWM_Init) refers to stm32f10x_tim.o(.text.TIM_OC1PreloadConfig) for TIM_OC1PreloadConfig
    pwm.o(.text.PWM_Init) refers to stm32f10x_tim.o(.text.TIM_OC4Init) for TIM_OC4Init
    pwm.o(.text.PWM_Init) refers to stm32f10x_tim.o(.text.TIM_OC4PreloadConfig) for TIM_OC4PreloadConfig
    pwm.o(.text.PWM_Init) refers to stm32f10x_tim.o(.text.TIM_OC2Init) for TIM_OC2Init
    pwm.o(.text.PWM_Init) refers to stm32f10x_tim.o(.text.TIM_OC2PreloadConfig) for TIM_OC2PreloadConfig
    pwm.o(.text.PWM_Init) refers to stm32f10x_tim.o(.text.TIM_ARRPreloadConfig) for TIM_ARRPreloadConfig
    pwm.o(.ARM.exidx.text.PWM_Init) refers to pwm.o(.text.PWM_Init) for [Anonymous Symbol]
    pwm.o(.text.PWM_SetDutyCycle) refers to pwm.o(.bss.pwmPeriod) for pwmPeriod
    pwm.o(.text.PWM_SetDutyCycle) refers to fflt_clz.o(x$fpl$ffltu) for __aeabi_ui2f
    pwm.o(.text.PWM_SetDutyCycle) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    pwm.o(.text.PWM_SetDutyCycle) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    pwm.o(.text.PWM_SetDutyCycle) refers to ffix.o(x$fpl$ffix) for __aeabi_f2iz
    pwm.o(.text.PWM_SetDutyCycle) refers to stm32f10x_tim.o(.text.TIM_SetCompare1) for TIM_SetCompare1
    pwm.o(.text.PWM_SetDutyCycle) refers to stm32f10x_tim.o(.text.TIM_SetCompare4) for TIM_SetCompare4
    pwm.o(.ARM.exidx.text.PWM_SetDutyCycle) refers to pwm.o(.text.PWM_SetDutyCycle) for [Anonymous Symbol]
    pwm.o(.text.PWM_Start) refers to stm32f10x_tim.o(.text.TIM_Cmd) for TIM_Cmd
    pwm.o(.text.PWM_Start) refers to stm32f10x_tim.o(.text.TIM_CtrlPWMOutputs) for TIM_CtrlPWMOutputs
    pwm.o(.ARM.exidx.text.PWM_Start) refers to pwm.o(.text.PWM_Start) for [Anonymous Symbol]
    pwm.o(.text.PWM_Stop) refers to stm32f10x_tim.o(.text.TIM_CtrlPWMOutputs) for TIM_CtrlPWMOutputs
    pwm.o(.text.PWM_Stop) refers to stm32f10x_tim.o(.text.TIM_Cmd) for TIM_Cmd
    pwm.o(.ARM.exidx.text.PWM_Stop) refers to pwm.o(.text.PWM_Stop) for [Anonymous Symbol]
    usart.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart.o(.text) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    usart.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart.o(.text) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    usart.o(.text.USART1_Init) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart.o(.text.USART1_Init) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart.o(.text.USART1_Init) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    usart.o(.text.USART1_Init) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart.o(.text.USART1_Init) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    usart.o(.text.USART1_Init) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    usart.o(.text.USART1_Init) refers to stm32f10x_gpio.o(.text.GPIO_Init) for GPIO_Init
    usart.o(.text.USART1_Init) refers to stm32f10x_usart.o(.text.USART_Init) for USART_Init
    usart.o(.text.USART1_Init) refers to stm32f10x_usart.o(.text.USART_ITConfig) for USART_ITConfig
    usart.o(.text.USART1_Init) refers to misc.o(.text.NVIC_Init) for NVIC_Init
    usart.o(.text.USART1_Init) refers to stm32f10x_usart.o(.text.USART_Cmd) for USART_Cmd
    usart.o(.ARM.exidx.text.USART1_Init) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart.o(.ARM.exidx.text.USART1_Init) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart.o(.ARM.exidx.text.USART1_Init) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    usart.o(.ARM.exidx.text.USART1_Init) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart.o(.ARM.exidx.text.USART1_Init) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    usart.o(.ARM.exidx.text.USART1_Init) refers to usart.o(.text.USART1_Init) for [Anonymous Symbol]
    usart.o(.text.USART1_SendChar) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart.o(.text.USART1_SendChar) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart.o(.text.USART1_SendChar) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    usart.o(.text.USART1_SendChar) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart.o(.text.USART1_SendChar) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    usart.o(.text.USART1_SendChar) refers to stm32f10x_usart.o(.text.USART_GetFlagStatus) for USART_GetFlagStatus
    usart.o(.text.USART1_SendChar) refers to stm32f10x_usart.o(.text.USART_SendData) for USART_SendData
    usart.o(.ARM.exidx.text.USART1_SendChar) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart.o(.ARM.exidx.text.USART1_SendChar) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart.o(.ARM.exidx.text.USART1_SendChar) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    usart.o(.ARM.exidx.text.USART1_SendChar) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart.o(.ARM.exidx.text.USART1_SendChar) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    usart.o(.ARM.exidx.text.USART1_SendChar) refers to usart.o(.text.USART1_SendChar) for [Anonymous Symbol]
    usart.o(.text.USART1_SendString) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart.o(.text.USART1_SendString) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart.o(.text.USART1_SendString) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    usart.o(.text.USART1_SendString) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart.o(.text.USART1_SendString) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    usart.o(.text.USART1_SendString) refers to stm32f10x_usart.o(.text.USART_GetFlagStatus) for USART_GetFlagStatus
    usart.o(.text.USART1_SendString) refers to stm32f10x_usart.o(.text.USART_SendData) for USART_SendData
    usart.o(.ARM.exidx.text.USART1_SendString) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart.o(.ARM.exidx.text.USART1_SendString) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart.o(.ARM.exidx.text.USART1_SendString) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    usart.o(.ARM.exidx.text.USART1_SendString) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart.o(.ARM.exidx.text.USART1_SendString) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    usart.o(.ARM.exidx.text.USART1_SendString) refers to usart.o(.text.USART1_SendString) for [Anonymous Symbol]
    usart.o(.text.USART1_Printf) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart.o(.text.USART1_Printf) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart.o(.text.USART1_Printf) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    usart.o(.text.USART1_Printf) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart.o(.text.USART1_Printf) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    usart.o(.text.USART1_Printf) refers to vsnprintf.o(.text) for vsnprintf
    usart.o(.text.USART1_Printf) refers to stm32f10x_usart.o(.text.USART_GetFlagStatus) for USART_GetFlagStatus
    usart.o(.text.USART1_Printf) refers to stm32f10x_usart.o(.text.USART_SendData) for USART_SendData
    usart.o(.ARM.exidx.text.USART1_Printf) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart.o(.ARM.exidx.text.USART1_Printf) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart.o(.ARM.exidx.text.USART1_Printf) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    usart.o(.ARM.exidx.text.USART1_Printf) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart.o(.ARM.exidx.text.USART1_Printf) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    usart.o(.ARM.exidx.text.USART1_Printf) refers to usart.o(.text.USART1_Printf) for [Anonymous Symbol]
    usart.o(.text.USART1_RxCpltCallback) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart.o(.text.USART1_RxCpltCallback) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart.o(.text.USART1_RxCpltCallback) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    usart.o(.text.USART1_RxCpltCallback) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart.o(.text.USART1_RxCpltCallback) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    usart.o(.text.USART1_RxCpltCallback) refers to usart.o(.bss.USART1_RxCpltCallback.cmd_index) for USART1_RxCpltCallback.cmd_index
    usart.o(.text.USART1_RxCpltCallback) refers to stm32f10x_usart.o(.text.USART_GetFlagStatus) for USART_GetFlagStatus
    usart.o(.text.USART1_RxCpltCallback) refers to stm32f10x_usart.o(.text.USART_SendData) for USART_SendData
    usart.o(.text.USART1_RxCpltCallback) refers to usart.o(.bss.USART1_RxCpltCallback.cmd_buffer) for USART1_RxCpltCallback.cmd_buffer
    usart.o(.text.USART1_RxCpltCallback) refers to memcmp.o(.text) for memcmp
    usart.o(.text.USART1_RxCpltCallback) refers to strtol.o(.text) for strtol
    usart.o(.text.USART1_RxCpltCallback) refers to usart.o(.bss.g_set_value) for g_set_value
    usart.o(.text.USART1_RxCpltCallback) refers to fcmp.o(x$fpl$fcmp) for __aeabi_fcmplt
    usart.o(.text.USART1_RxCpltCallback) refers to pwm.o(.text.PWM_SetDutyCycle) for PWM_SetDutyCycle
    usart.o(.text.USART1_RxCpltCallback) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    usart.o(.text.USART1_RxCpltCallback) refers to __2snprintf.o(.text) for __2snprintf
    usart.o(.text.USART1_RxCpltCallback) refers to usart.o(.rodata.str1.1) for .L.str.1
    usart.o(.text.USART1_RxCpltCallback) refers to strtof.o(i.strtof) for strtof
    usart.o(.text.USART1_RxCpltCallback) refers to ffix.o(x$fpl$ffix) for __aeabi_f2iz
    usart.o(.text.USART1_RxCpltCallback) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    usart.o(.text.USART1_RxCpltCallback) refers to usart.o(.text.USART1_SendString) for USART1_SendString
    usart.o(.ARM.exidx.text.USART1_RxCpltCallback) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart.o(.ARM.exidx.text.USART1_RxCpltCallback) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart.o(.ARM.exidx.text.USART1_RxCpltCallback) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    usart.o(.ARM.exidx.text.USART1_RxCpltCallback) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart.o(.ARM.exidx.text.USART1_RxCpltCallback) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    usart.o(.ARM.exidx.text.USART1_RxCpltCallback) refers to usart.o(.text.USART1_RxCpltCallback) for [Anonymous Symbol]
    usart.o(.text.USART1_IRQHandler) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart.o(.text.USART1_IRQHandler) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart.o(.text.USART1_IRQHandler) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    usart.o(.text.USART1_IRQHandler) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart.o(.text.USART1_IRQHandler) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    usart.o(.text.USART1_IRQHandler) refers to stm32f10x_usart.o(.text.USART_GetITStatus) for USART_GetITStatus
    usart.o(.text.USART1_IRQHandler) refers to stm32f10x_usart.o(.text.USART_ReceiveData) for USART_ReceiveData
    usart.o(.text.USART1_IRQHandler) refers to usart.o(.text.USART1_RxCpltCallback) for USART1_RxCpltCallback
    usart.o(.text.USART1_IRQHandler) refers to stm32f10x_usart.o(.text.USART_ITConfig) for USART_ITConfig
    usart.o(.text.USART1_IRQHandler) refers to stm32f10x_usart.o(.text.USART_ClearITPendingBit) for USART_ClearITPendingBit
    usart.o(.ARM.exidx.text.USART1_IRQHandler) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart.o(.ARM.exidx.text.USART1_IRQHandler) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart.o(.ARM.exidx.text.USART1_IRQHandler) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    usart.o(.ARM.exidx.text.USART1_IRQHandler) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart.o(.ARM.exidx.text.USART1_IRQHandler) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    usart.o(.ARM.exidx.text.USART1_IRQHandler) refers to usart.o(.text.USART1_IRQHandler) for [Anonymous Symbol]
    usart.o(.text.USART1_ReceiveChar) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart.o(.text.USART1_ReceiveChar) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart.o(.text.USART1_ReceiveChar) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    usart.o(.text.USART1_ReceiveChar) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart.o(.text.USART1_ReceiveChar) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    usart.o(.text.USART1_ReceiveChar) refers to stm32f10x_usart.o(.text.USART_GetFlagStatus) for USART_GetFlagStatus
    usart.o(.text.USART1_ReceiveChar) refers to stm32f10x_usart.o(.text.USART_ReceiveData) for USART_ReceiveData
    usart.o(.ARM.exidx.text.USART1_ReceiveChar) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart.o(.ARM.exidx.text.USART1_ReceiveChar) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart.o(.ARM.exidx.text.USART1_ReceiveChar) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    usart.o(.ARM.exidx.text.USART1_ReceiveChar) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart.o(.ARM.exidx.text.USART1_ReceiveChar) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    usart.o(.ARM.exidx.text.USART1_ReceiveChar) refers to usart.o(.text.USART1_ReceiveChar) for [Anonymous Symbol]
    usart.o(.text.USART1_ReceiveString) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart.o(.text.USART1_ReceiveString) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart.o(.text.USART1_ReceiveString) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    usart.o(.text.USART1_ReceiveString) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart.o(.text.USART1_ReceiveString) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    usart.o(.text.USART1_ReceiveString) refers to stm32f10x_usart.o(.text.USART_GetFlagStatus) for USART_GetFlagStatus
    usart.o(.text.USART1_ReceiveString) refers to stm32f10x_usart.o(.text.USART_ReceiveData) for USART_ReceiveData
    usart.o(.ARM.exidx.text.USART1_ReceiveString) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart.o(.ARM.exidx.text.USART1_ReceiveString) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart.o(.ARM.exidx.text.USART1_ReceiveString) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    usart.o(.ARM.exidx.text.USART1_ReceiveString) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart.o(.ARM.exidx.text.USART1_ReceiveString) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    usart.o(.ARM.exidx.text.USART1_ReceiveString) refers to usart.o(.text.USART1_ReceiveString) for [Anonymous Symbol]
    usart.o(.bss.USART1_RxCpltCallback.cmd_buffer) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart.o(.bss.USART1_RxCpltCallback.cmd_buffer) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart.o(.bss.USART1_RxCpltCallback.cmd_buffer) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    usart.o(.bss.USART1_RxCpltCallback.cmd_buffer) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart.o(.bss.USART1_RxCpltCallback.cmd_buffer) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    usart.o(.bss.USART1_RxCpltCallback.cmd_index) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart.o(.bss.USART1_RxCpltCallback.cmd_index) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart.o(.bss.USART1_RxCpltCallback.cmd_index) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    usart.o(.bss.USART1_RxCpltCallback.cmd_index) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart.o(.bss.USART1_RxCpltCallback.cmd_index) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    usart.o(.rodata.str1.1) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart.o(.rodata.str1.1) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart.o(.rodata.str1.1) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    usart.o(.rodata.str1.1) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart.o(.rodata.str1.1) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    usart.o(.bss.g_set_value) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    usart.o(.bss.g_set_value) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    usart.o(.bss.g_set_value) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    usart.o(.bss.g_set_value) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    usart.o(.bss.g_set_value) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    timer.o(.text.TIM3_Config) refers to stm32f10x_rcc.o(.text.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    timer.o(.text.TIM3_Config) refers to stm32f10x_tim.o(.text.TIM_TimeBaseInit) for TIM_TimeBaseInit
    timer.o(.text.TIM3_Config) refers to stm32f10x_tim.o(.text.TIM_ITConfig) for TIM_ITConfig
    timer.o(.text.TIM3_Config) refers to misc.o(.text.NVIC_Init) for NVIC_Init
    timer.o(.text.TIM3_Config) refers to stm32f10x_tim.o(.text.TIM_Cmd) for TIM_Cmd
    timer.o(.ARM.exidx.text.TIM3_Config) refers to timer.o(.text.TIM3_Config) for [Anonymous Symbol]
    timer.o(.text.TIM3_IRQHandler) refers to stm32f10x_tim.o(.text.TIM_GetITStatus) for TIM_GetITStatus
    timer.o(.text.TIM3_IRQHandler) refers to stm32f10x_tim.o(.text.TIM_ClearITPendingBit) for TIM_ClearITPendingBit
    timer.o(.text.TIM3_IRQHandler) refers to main.o(.bss.motor) for motor
    timer.o(.text.TIM3_IRQHandler) refers to motor_control.o(.text.MotorControl_Update) for MotorControl_Update
    timer.o(.ARM.exidx.text.TIM3_IRQHandler) refers to timer.o(.text.TIM3_IRQHandler) for [Anonymous Symbol]
    encoder.o(.text.EXTI0_IRQHandler) refers to stm32f10x_exti.o(.text.EXTI_GetITStatus) for EXTI_GetITStatus
    encoder.o(.text.EXTI0_IRQHandler) refers to stm32f10x_gpio.o(.text.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    encoder.o(.text.EXTI0_IRQHandler) refers to encoder.o(.bss.encoder) for encoder
    encoder.o(.text.EXTI0_IRQHandler) refers to stm32f10x_exti.o(.text.EXTI_ClearITPendingBit) for EXTI_ClearITPendingBit
    encoder.o(.ARM.exidx.text.EXTI0_IRQHandler) refers to encoder.o(.text.EXTI0_IRQHandler) for [Anonymous Symbol]
    encoder.o(.text.EXTI2_IRQHandler) refers to stm32f10x_exti.o(.text.EXTI_GetITStatus) for EXTI_GetITStatus
    encoder.o(.text.EXTI2_IRQHandler) refers to stm32f10x_tim.o(.text.TIM_GetCounter) for TIM_GetCounter
    encoder.o(.text.EXTI2_IRQHandler) refers to encoder.o(.data.EXTI2_IRQHandler.last_tim2_count) for EXTI2_IRQHandler.last_tim2_count
    encoder.o(.text.EXTI2_IRQHandler) refers to stm32f10x_gpio.o(.text.GPIO_ReadInputDataBit) for GPIO_ReadInputDataBit
    encoder.o(.text.EXTI2_IRQHandler) refers to encoder.o(.bss.encoder) for encoder
    encoder.o(.text.EXTI2_IRQHandler) refers to stm32f10x_exti.o(.text.EXTI_ClearITPendingBit) for EXTI_ClearITPendingBit
    encoder.o(.ARM.exidx.text.EXTI2_IRQHandler) refers to encoder.o(.text.EXTI2_IRQHandler) for [Anonymous Symbol]
    encoder.o(.text.TIM2_IRQHandler) refers to stm32f10x_tim.o(.text.TIM_GetITStatus) for TIM_GetITStatus
    encoder.o(.text.TIM2_IRQHandler) refers to encoder.o(.bss.encoder) for encoder
    encoder.o(.text.TIM2_IRQHandler) refers to encoder.o(.bss.TIM2_IRQHandler.last_absolute_count) for TIM2_IRQHandler.last_absolute_count
    encoder.o(.text.TIM2_IRQHandler) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    encoder.o(.text.TIM2_IRQHandler) refers to stm32f10x_tim.o(.text.TIM_ClearITPendingBit) for TIM_ClearITPendingBit
    encoder.o(.ARM.exidx.text.TIM2_IRQHandler) refers to encoder.o(.text.TIM2_IRQHandler) for [Anonymous Symbol]
    encoder.o(.text.Encoder_Init) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    encoder.o(.text.Encoder_Init) refers to stm32f10x_rcc.o(.text.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    encoder.o(.text.Encoder_Init) refers to stm32f10x_gpio.o(.text.GPIO_Init) for GPIO_Init
    encoder.o(.text.Encoder_Init) refers to stm32f10x_gpio.o(.text.GPIO_EXTILineConfig) for GPIO_EXTILineConfig
    encoder.o(.text.Encoder_Init) refers to stm32f10x_exti.o(.text.EXTI_Init) for EXTI_Init
    encoder.o(.text.Encoder_Init) refers to misc.o(.text.NVIC_Init) for NVIC_Init
    encoder.o(.text.Encoder_Init) refers to stm32f10x_tim.o(.text.TIM_TimeBaseInit) for TIM_TimeBaseInit
    encoder.o(.text.Encoder_Init) refers to stm32f10x_tim.o(.text.TIM_ITConfig) for TIM_ITConfig
    encoder.o(.text.Encoder_Init) refers to stm32f10x_tim.o(.text.TIM_Cmd) for TIM_Cmd
    encoder.o(.ARM.exidx.text.Encoder_Init) refers to encoder.o(.text.Encoder_Init) for [Anonymous Symbol]
    encoder.o(.text.Encoder_GetData) refers to encoder.o(.bss.encoder) for encoder
    encoder.o(.ARM.exidx.text.Encoder_GetData) refers to encoder.o(.text.Encoder_GetData) for [Anonymous Symbol]
    encoder.o(.text.Show_Encoder) refers to encoder.o(.bss.encoder) for encoder
    encoder.o(.text.Show_Encoder) refers to oled.o(.text.OLED_ShowSignedNum) for OLED_ShowSignedNum
    encoder.o(.text.Show_Encoder) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    encoder.o(.text.Show_Encoder) refers to oled.o(.text.OLED_ShowFloatNum) for OLED_ShowFloatNum
    encoder.o(.text.Show_Encoder) refers to oled.o(.text.OLED_Update) for OLED_Update
    encoder.o(.ARM.exidx.text.Show_Encoder) refers to encoder.o(.text.Show_Encoder) for [Anonymous Symbol]
    pid.o(.ARM.exidx.text.PID_Init) refers to pid.o(.text.PID_Init) for [Anonymous Symbol]
    pid.o(.ARM.exidx.text.PID_SetTarget) refers to pid.o(.text.PID_SetTarget) for [Anonymous Symbol]
    pid.o(.text.PID_Update) refers to faddsub_clz.o(x$fpl$fsub) for __aeabi_fsub
    pid.o(.text.PID_Update) refers to fmul.o(x$fpl$fmul) for __aeabi_fmul
    pid.o(.text.PID_Update) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    pid.o(.text.PID_Update) refers to fcmp.o(x$fpl$fcmp) for __aeabi_fcmpgt
    pid.o(.ARM.exidx.text.PID_Update) refers to pid.o(.text.PID_Update) for [Anonymous Symbol]
    oled.o(.text.OLED_W_SCL) refers to stm32f10x_gpio.o(.text.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(.ARM.exidx.text.OLED_W_SCL) refers to oled.o(.text.OLED_W_SCL) for [Anonymous Symbol]
    oled.o(.text.OLED_W_SDA) refers to stm32f10x_gpio.o(.text.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(.ARM.exidx.text.OLED_W_SDA) refers to oled.o(.text.OLED_W_SDA) for [Anonymous Symbol]
    oled.o(.text.OLED_GPIO_Init) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    oled.o(.text.OLED_GPIO_Init) refers to stm32f10x_gpio.o(.text.GPIO_Init) for GPIO_Init
    oled.o(.text.OLED_GPIO_Init) refers to stm32f10x_gpio.o(.text.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(.ARM.exidx.text.OLED_GPIO_Init) refers to oled.o(.text.OLED_GPIO_Init) for [Anonymous Symbol]
    oled.o(.text.OLED_I2C_Start) refers to stm32f10x_gpio.o(.text.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(.ARM.exidx.text.OLED_I2C_Start) refers to oled.o(.text.OLED_I2C_Start) for [Anonymous Symbol]
    oled.o(.text.OLED_I2C_Stop) refers to stm32f10x_gpio.o(.text.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(.ARM.exidx.text.OLED_I2C_Stop) refers to oled.o(.text.OLED_I2C_Stop) for [Anonymous Symbol]
    oled.o(.text.OLED_I2C_SendByte) refers to stm32f10x_gpio.o(.text.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(.ARM.exidx.text.OLED_I2C_SendByte) refers to oled.o(.text.OLED_I2C_SendByte) for [Anonymous Symbol]
    oled.o(.text.OLED_WriteCommand) refers to stm32f10x_gpio.o(.text.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(.text.OLED_WriteCommand) refers to oled.o(.text.OLED_I2C_SendByte) for OLED_I2C_SendByte
    oled.o(.ARM.exidx.text.OLED_WriteCommand) refers to oled.o(.text.OLED_WriteCommand) for [Anonymous Symbol]
    oled.o(.text.OLED_WriteData) refers to stm32f10x_gpio.o(.text.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(.text.OLED_WriteData) refers to oled.o(.text.OLED_I2C_SendByte) for OLED_I2C_SendByte
    oled.o(.ARM.exidx.text.OLED_WriteData) refers to oled.o(.text.OLED_WriteData) for [Anonymous Symbol]
    oled.o(.text.OLED_Init) refers to stm32f10x_rcc.o(.text.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    oled.o(.text.OLED_Init) refers to stm32f10x_gpio.o(.text.GPIO_Init) for GPIO_Init
    oled.o(.text.OLED_Init) refers to stm32f10x_gpio.o(.text.GPIO_WriteBit) for GPIO_WriteBit
    oled.o(.text.OLED_Init) refers to oled.o(.text.OLED_WriteCommand) for OLED_WriteCommand
    oled.o(.text.OLED_Init) refers to oled.o(.bss.OLED_DisplayBuf) for OLED_DisplayBuf
    oled.o(.text.OLED_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    oled.o(.text.OLED_Init) refers to oled.o(.text.OLED_WriteData) for OLED_WriteData
    oled.o(.ARM.exidx.text.OLED_Init) refers to oled.o(.text.OLED_Init) for [Anonymous Symbol]
    oled.o(.text.OLED_Clear) refers to oled.o(.bss.OLED_DisplayBuf) for OLED_DisplayBuf
    oled.o(.text.OLED_Clear) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    oled.o(.ARM.exidx.text.OLED_Clear) refers to oled.o(.text.OLED_Clear) for [Anonymous Symbol]
    oled.o(.text.OLED_Update) refers to oled.o(.bss.OLED_DisplayBuf) for OLED_DisplayBuf
    oled.o(.text.OLED_Update) refers to oled.o(.text.OLED_WriteCommand) for OLED_WriteCommand
    oled.o(.text.OLED_Update) refers to oled.o(.text.OLED_WriteData) for OLED_WriteData
    oled.o(.ARM.exidx.text.OLED_Update) refers to oled.o(.text.OLED_Update) for [Anonymous Symbol]
    oled.o(.text.OLED_SetCursor) refers to oled.o(.text.OLED_WriteCommand) for OLED_WriteCommand
    oled.o(.ARM.exidx.text.OLED_SetCursor) refers to oled.o(.text.OLED_SetCursor) for [Anonymous Symbol]
    oled.o(.ARM.exidx.text.OLED_Pow) refers to oled.o(.text.OLED_Pow) for [Anonymous Symbol]
    oled.o(.ARM.exidx.text.OLED_pnpoly) refers to oled.o(.text.OLED_pnpoly) for [Anonymous Symbol]
    oled.o(.text.OLED_IsInAngle) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    oled.o(.text.OLED_IsInAngle) refers to atan2.o(i.atan2) for atan2
    oled.o(.text.OLED_IsInAngle) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    oled.o(.text.OLED_IsInAngle) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    oled.o(.text.OLED_IsInAngle) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    oled.o(.ARM.exidx.text.OLED_IsInAngle) refers to oled.o(.text.OLED_IsInAngle) for [Anonymous Symbol]
    oled.o(.text.OLED_UpdateArea) refers to oled.o(.bss.OLED_DisplayBuf) for OLED_DisplayBuf
    oled.o(.text.OLED_UpdateArea) refers to oled.o(.text.OLED_WriteCommand) for OLED_WriteCommand
    oled.o(.text.OLED_UpdateArea) refers to oled.o(.text.OLED_WriteData) for OLED_WriteData
    oled.o(.ARM.exidx.text.OLED_UpdateArea) refers to oled.o(.text.OLED_UpdateArea) for [Anonymous Symbol]
    oled.o(.text.OLED_ClearArea) refers to oled.o(.bss.OLED_DisplayBuf) for OLED_DisplayBuf
    oled.o(.ARM.exidx.text.OLED_ClearArea) refers to oled.o(.text.OLED_ClearArea) for [Anonymous Symbol]
    oled.o(.text.OLED_Reverse) refers to oled.o(.bss.OLED_DisplayBuf) for OLED_DisplayBuf
    oled.o(.ARM.exidx.text.OLED_Reverse) refers to oled.o(.text.OLED_Reverse) for [Anonymous Symbol]
    oled.o(.text.OLED_ReverseArea) refers to oled.o(.bss.OLED_DisplayBuf) for OLED_DisplayBuf
    oled.o(.ARM.exidx.text.OLED_ReverseArea) refers to oled.o(.text.OLED_ReverseArea) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowChar) refers to oled_data.o(.rodata.OLED_F8x16) for OLED_F8x16
    oled.o(.text.OLED_ShowChar) refers to oled_data.o(.rodata.OLED_F6x8) for OLED_F6x8
    oled.o(.text.OLED_ShowChar) refers to oled.o(.text.OLED_ShowImage) for OLED_ShowImage
    oled.o(.ARM.exidx.text.OLED_ShowChar) refers to oled.o(.text.OLED_ShowChar) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowImage) refers to oled.o(.bss.OLED_DisplayBuf) for OLED_DisplayBuf
    oled.o(.ARM.exidx.text.OLED_ShowImage) refers to oled.o(.text.OLED_ShowImage) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowString) refers to oled_data.o(.rodata.OLED_CF16x16) for OLED_CF16x16
    oled.o(.text.OLED_ShowString) refers to oled_data.o(.rodata.OLED_F6x8) for OLED_F6x8
    oled.o(.text.OLED_ShowString) refers to oled.o(.text.OLED_ShowImage) for OLED_ShowImage
    oled.o(.text.OLED_ShowString) refers to oled_data.o(.rodata.OLED_F8x16) for OLED_F8x16
    oled.o(.text.OLED_ShowString) refers to strcmpv7m.o(.text) for strcmp
    oled.o(.ARM.exidx.text.OLED_ShowString) refers to oled.o(.text.OLED_ShowString) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowNum) refers to oled_data.o(.rodata.OLED_F6x8) for OLED_F6x8
    oled.o(.text.OLED_ShowNum) refers to oled.o(.text.OLED_ShowImage) for OLED_ShowImage
    oled.o(.text.OLED_ShowNum) refers to oled_data.o(.rodata.OLED_F8x16) for OLED_F8x16
    oled.o(.ARM.exidx.text.OLED_ShowNum) refers to oled.o(.text.OLED_ShowNum) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowSignedNum) refers to oled_data.o(.rodata.OLED_F8x16) for OLED_F8x16
    oled.o(.text.OLED_ShowSignedNum) refers to oled_data.o(.rodata.OLED_F6x8) for OLED_F6x8
    oled.o(.text.OLED_ShowSignedNum) refers to oled.o(.text.OLED_ShowImage) for OLED_ShowImage
    oled.o(.ARM.exidx.text.OLED_ShowSignedNum) refers to oled.o(.text.OLED_ShowSignedNum) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowHexNum) refers to oled.o(.text.OLED_ShowImage) for OLED_ShowImage
    oled.o(.text.OLED_ShowHexNum) refers to oled_data.o(.rodata.OLED_F8x16) for OLED_F8x16
    oled.o(.text.OLED_ShowHexNum) refers to oled_data.o(.rodata.OLED_F6x8) for OLED_F6x8
    oled.o(.ARM.exidx.text.OLED_ShowHexNum) refers to oled.o(.text.OLED_ShowHexNum) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowBinNum) refers to oled_data.o(.rodata.OLED_F6x8) for OLED_F6x8
    oled.o(.text.OLED_ShowBinNum) refers to oled.o(.text.OLED_ShowImage) for OLED_ShowImage
    oled.o(.text.OLED_ShowBinNum) refers to oled_data.o(.rodata.OLED_F8x16) for OLED_F8x16
    oled.o(.ARM.exidx.text.OLED_ShowBinNum) refers to oled.o(.text.OLED_ShowBinNum) for [Anonymous Symbol]
    oled.o(.text.OLED_ShowFloatNum) refers to dcmp.o(x$fpl$fcmp) for __aeabi_dcmplt
    oled.o(.text.OLED_ShowFloatNum) refers to oled_data.o(.rodata.OLED_F8x16) for OLED_F8x16
    oled.o(.text.OLED_ShowFloatNum) refers to oled_data.o(.rodata.OLED_F6x8) for OLED_F6x8
    oled.o(.text.OLED_ShowFloatNum) refers to oled.o(.text.OLED_ShowImage) for OLED_ShowImage
    oled.o(.text.OLED_ShowFloatNum) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    oled.o(.text.OLED_ShowFloatNum) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    oled.o(.text.OLED_ShowFloatNum) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    oled.o(.text.OLED_ShowFloatNum) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    oled.o(.text.OLED_ShowFloatNum) refers to round.o(i.round) for round
    oled.o(.ARM.exidx.text.OLED_ShowFloatNum) refers to oled.o(.text.OLED_ShowFloatNum) for [Anonymous Symbol]
    oled.o(.text.OLED_Printf) refers to vsprintf.o(.text) for vsprintf
    oled.o(.text.OLED_Printf) refers to oled.o(.text.OLED_ShowString) for OLED_ShowString
    oled.o(.ARM.exidx.text.OLED_Printf) refers to oled.o(.text.OLED_Printf) for [Anonymous Symbol]
    oled.o(.text.OLED_DrawPoint) refers to oled.o(.bss.OLED_DisplayBuf) for OLED_DisplayBuf
    oled.o(.ARM.exidx.text.OLED_DrawPoint) refers to oled.o(.text.OLED_DrawPoint) for [Anonymous Symbol]
    oled.o(.text.OLED_GetPoint) refers to oled.o(.bss.OLED_DisplayBuf) for OLED_DisplayBuf
    oled.o(.ARM.exidx.text.OLED_GetPoint) refers to oled.o(.text.OLED_GetPoint) for [Anonymous Symbol]
    oled.o(.text.OLED_DrawLine) refers to oled.o(.bss.OLED_DisplayBuf) for OLED_DisplayBuf
    oled.o(.ARM.exidx.text.OLED_DrawLine) refers to oled.o(.text.OLED_DrawLine) for [Anonymous Symbol]
    oled.o(.text.OLED_DrawRectangle) refers to oled.o(.bss.OLED_DisplayBuf) for OLED_DisplayBuf
    oled.o(.ARM.exidx.text.OLED_DrawRectangle) refers to oled.o(.text.OLED_DrawRectangle) for [Anonymous Symbol]
    oled.o(.text.OLED_DrawTriangle) refers to oled.o(.bss.OLED_DisplayBuf) for OLED_DisplayBuf
    oled.o(.text.OLED_DrawTriangle) refers to oled.o(.text.OLED_DrawLine) for OLED_DrawLine
    oled.o(.ARM.exidx.text.OLED_DrawTriangle) refers to oled.o(.text.OLED_DrawTriangle) for [Anonymous Symbol]
    oled.o(.text.OLED_DrawCircle) refers to oled.o(.bss.OLED_DisplayBuf) for OLED_DisplayBuf
    oled.o(.ARM.exidx.text.OLED_DrawCircle) refers to oled.o(.text.OLED_DrawCircle) for [Anonymous Symbol]
    oled.o(.text.OLED_DrawEllipse) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    oled.o(.text.OLED_DrawEllipse) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    oled.o(.text.OLED_DrawEllipse) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    oled.o(.text.OLED_DrawEllipse) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    oled.o(.text.OLED_DrawEllipse) refers to oled.o(.bss.OLED_DisplayBuf) for OLED_DisplayBuf
    oled.o(.text.OLED_DrawEllipse) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    oled.o(.text.OLED_DrawEllipse) refers to dcmp.o(x$fpl$fcmp) for __aeabi_dcmple
    oled.o(.text.OLED_DrawEllipse) refers to fcmp.o(x$fpl$fcmp) for __aeabi_fcmpgt
    oled.o(.text.OLED_DrawEllipse) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    oled.o(.text.OLED_DrawEllipse) refers to faddsub_clz.o(x$fpl$fadd) for __aeabi_fadd
    oled.o(.text.OLED_DrawEllipse) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    oled.o(.ARM.exidx.text.OLED_DrawEllipse) refers to oled.o(.text.OLED_DrawEllipse) for [Anonymous Symbol]
    oled.o(.text.OLED_DrawArc) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    oled.o(.text.OLED_DrawArc) refers to atan2.o(i.atan2) for atan2
    oled.o(.text.OLED_DrawArc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    oled.o(.text.OLED_DrawArc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    oled.o(.text.OLED_DrawArc) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    oled.o(.text.OLED_DrawArc) refers to oled.o(.bss.OLED_DisplayBuf) for OLED_DisplayBuf
    oled.o(.text.OLED_DrawArc) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    oled.o(.ARM.exidx.text.OLED_DrawArc) refers to oled.o(.text.OLED_DrawArc) for [Anonymous Symbol]
    motor_control.o(.text.MotorControl_Init) refers to pid.o(.text.PID_Init) for PID_Init
    motor_control.o(.ARM.exidx.text.MotorControl_Init) refers to motor_control.o(.text.MotorControl_Init) for [Anonymous Symbol]
    motor_control.o(.text.MotorControl_SetSpeed) refers to pid.o(.text.PID_SetTarget) for PID_SetTarget
    motor_control.o(.ARM.exidx.text.MotorControl_SetSpeed) refers to motor_control.o(.text.MotorControl_SetSpeed) for [Anonymous Symbol]
    motor_control.o(.text.MotorControl_Update) refers to encoder.o(.text.Encoder_GetData) for Encoder_GetData
    motor_control.o(.text.MotorControl_Update) refers to pid.o(.text.PID_Update) for PID_Update
    motor_control.o(.text.MotorControl_Update) refers to pwm.o(.text.PWM_SetDutyCycle) for PWM_SetDutyCycle
    motor_control.o(.ARM.exidx.text.MotorControl_Update) refers to motor_control.o(.text.MotorControl_Update) for [Anonymous Symbol]
    vsprintf.o(.text) refers (Special) to _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) for _printf_a
    vsprintf.o(.text) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    vsprintf.o(.text) refers (Special) to _printf_charcount.o(.text) for _printf_charcount
    vsprintf.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    vsprintf.o(.text) refers (Special) to _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) for _printf_e
    vsprintf.o(.text) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    vsprintf.o(.text) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    vsprintf.o(.text) refers (Special) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    vsprintf.o(.text) refers (Special) to _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) for _printf_g
    vsprintf.o(.text) refers (Special) to _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) for _printf_i
    vsprintf.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    vsprintf.o(.text) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    vsprintf.o(.text) refers (Special) to _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) for _printf_lc
    vsprintf.o(.text) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    vsprintf.o(.text) refers (Special) to _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) for _printf_lld
    vsprintf.o(.text) refers (Special) to _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) for _printf_lli
    vsprintf.o(.text) refers (Special) to _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) for _printf_llo
    vsprintf.o(.text) refers (Special) to _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) for _printf_llu
    vsprintf.o(.text) refers (Special) to _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) for _printf_llx
    vsprintf.o(.text) refers (Special) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    vsprintf.o(.text) refers (Special) to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    vsprintf.o(.text) refers (Special) to _printf_oct_int_ll.o(.text) for _printf_longlong_oct
    vsprintf.o(.text) refers (Special) to _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) for _printf_ls
    vsprintf.o(.text) refers (Special) to _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) for _printf_n
    vsprintf.o(.text) refers (Special) to _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) for _printf_o
    vsprintf.o(.text) refers (Special) to _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) for _printf_p
    vsprintf.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    vsprintf.o(.text) refers (Special) to _printf_pad.o(.text) for _printf_post_padding
    vsprintf.o(.text) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    vsprintf.o(.text) refers (Special) to _printf_str.o(.text) for _printf_str
    vsprintf.o(.text) refers (Special) to _printf_truncate.o(.text) for _printf_truncate_signed
    vsprintf.o(.text) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    vsprintf.o(.text) refers (Special) to _printf_wctomb.o(.text) for _printf_wctomb
    vsprintf.o(.text) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    vsprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    vsprintf.o(.text) refers to _sputc.o(.text) for _sputc
    vsnprintf.o(.text) refers (Special) to _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) for _printf_a
    vsnprintf.o(.text) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    vsnprintf.o(.text) refers (Special) to _printf_charcount.o(.text) for _printf_charcount
    vsnprintf.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    vsnprintf.o(.text) refers (Special) to _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) for _printf_e
    vsnprintf.o(.text) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    vsnprintf.o(.text) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    vsnprintf.o(.text) refers (Special) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    vsnprintf.o(.text) refers (Special) to _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) for _printf_g
    vsnprintf.o(.text) refers (Special) to _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) for _printf_i
    vsnprintf.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    vsnprintf.o(.text) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    vsnprintf.o(.text) refers (Special) to _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) for _printf_lc
    vsnprintf.o(.text) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    vsnprintf.o(.text) refers (Special) to _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) for _printf_lld
    vsnprintf.o(.text) refers (Special) to _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) for _printf_lli
    vsnprintf.o(.text) refers (Special) to _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) for _printf_llo
    vsnprintf.o(.text) refers (Special) to _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) for _printf_llu
    vsnprintf.o(.text) refers (Special) to _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) for _printf_llx
    vsnprintf.o(.text) refers (Special) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    vsnprintf.o(.text) refers (Special) to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    vsnprintf.o(.text) refers (Special) to _printf_oct_int_ll.o(.text) for _printf_longlong_oct
    vsnprintf.o(.text) refers (Special) to _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) for _printf_ls
    vsnprintf.o(.text) refers (Special) to _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) for _printf_n
    vsnprintf.o(.text) refers (Special) to _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) for _printf_o
    vsnprintf.o(.text) refers (Special) to _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) for _printf_p
    vsnprintf.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    vsnprintf.o(.text) refers (Special) to _printf_pad.o(.text) for _printf_post_padding
    vsnprintf.o(.text) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    vsnprintf.o(.text) refers (Special) to _printf_str.o(.text) for _printf_str
    vsnprintf.o(.text) refers (Special) to _printf_truncate.o(.text) for _printf_truncate_signed
    vsnprintf.o(.text) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    vsnprintf.o(.text) refers (Special) to _printf_wctomb.o(.text) for _printf_wctomb
    vsnprintf.o(.text) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    vsnprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    vsnprintf.o(.text) refers to _snputc.o(.text) for _snputc
    __2snprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    __2snprintf.o(.text) refers to _snputc.o(.text) for _snputc
    noretval__2snprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    noretval__2snprintf.o(.text) refers to _snputc.o(.text) for _snputc
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_signed
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    strtol.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    strtol.o(.text) refers to _strtoul.o(.text) for _strtoul
    strtol.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    d2f.o(x$fpl$d2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    d2f.o(x$fpl$d2f) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$dadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dadd) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    daddsub_clz.o(x$fpl$dadd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$drsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dsub) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$dsub) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dcmp.o(x$fpl$fcmp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcmp.o(x$fpl$fcmp) refers to deqf.o(x$fpl$deqf) for _dcmpeq
    dcmp.o(x$fpl$fcmp) refers to dgeqf.o(x$fpl$dgeqf) for _dcmpge
    dcmp.o(x$fpl$fcmp) refers to dleqf.o(x$fpl$dleqf) for _dcmple
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfix) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfix) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfixr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfixr) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixu) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixur) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixur) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dflt_clz.o(x$fpl$dfltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dfltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    faddsub_clz.o(x$fpl$fadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$fadd) refers to faddsub_clz.o(x$fpl$fsub) for _fsub1
    faddsub_clz.o(x$fpl$fadd) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    faddsub_clz.o(x$fpl$fadd) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    faddsub_clz.o(x$fpl$frsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$frsb) refers to faddsub_clz.o(x$fpl$fadd) for _fadd1
    faddsub_clz.o(x$fpl$frsb) refers to faddsub_clz.o(x$fpl$fsub) for _fsub1
    faddsub_clz.o(x$fpl$fsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    faddsub_clz.o(x$fpl$fsub) refers to faddsub_clz.o(x$fpl$fadd) for _fadd1
    faddsub_clz.o(x$fpl$fsub) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fcmp.o(x$fpl$fcmp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmp.o(x$fpl$fcmp) refers to feqf.o(x$fpl$feqf) for _fcmpeq
    fcmp.o(x$fpl$fcmp) refers to fgeqf.o(x$fpl$fgeqf) for _fcmpge
    fcmp.o(x$fpl$fcmp) refers to fleqf.o(x$fpl$fleqf) for _fcmple
    fdiv.o(x$fpl$frdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(x$fpl$frdiv) refers to fdiv.o(x$fpl$fdiv) for _fdiv1
    fdiv.o(x$fpl$fdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(x$fpl$fdiv) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    fdiv.o(x$fpl$fdiv) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffix.o(x$fpl$ffix) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffix.o(x$fpl$ffix) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    ffix.o(x$fpl$ffixr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ffix.o(x$fpl$ffixr) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fflt_clz.o(x$fpl$ffltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$fflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$ffltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmul.o(x$fpl$fmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fmul.o(x$fpl$fmul) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    fmul.o(x$fpl$fmul) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    atan2.o(i.__softfp_atan2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.__softfp_atan2) refers to atan2.o(i.atan2) for atan2
    atan2.o(i.atan2) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2.o(i.atan2) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2.o(i.atan2) refers to atan.o(i.atan) for atan
    atan2.o(i.atan2) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan2.o(i.atan2) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan2.o(i.atan2) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    atan2.o(i.atan2) refers to _rserrno.o(.text) for __set_errno
    atan2.o(i.atan2) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan2.o(i.atan2) refers to qnan.o(.constdata) for __mathlib_zero
    round.o(i.round) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    round.o(i.round) refers to drnd.o(x$fpl$drnd) for _drnd
    round.o(i.round) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    round.o(i.round) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    round.o(i.round) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    round.o(i.round) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    round.o(i.round) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    strtof.o(i.__softfp_strtof) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    strtof.o(i.__softfp_strtof) refers to strtof.o(.text) for __strtof_int
    strtof.o(i.strtof) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    strtof.o(i.strtof) refers to strtof.o(.text) for __strtof_int
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    rt_ctype_table.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    rt_ctype_table.o(.text) refers to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_dec_accurate.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec_accurate.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec_accurate.o(.text) refers to btod_accurate.o(.text) for _btod_main
    _printf_fp_dec_accurate.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec_accurate.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec_accurate.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec_accurate.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec_accurate.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec_accurate.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_char_common.o(.text) refers to __printf_flags_ss_wp.o(.text) for __printf
    _printf_wctomb.o(.text) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_wctomb.o(.text) refers to _c16rtomb.o(.text) for _wcrtomb
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_wctomb.o(.text) refers to _printf_wctomb.o(.constdata) for .constdata
    _printf_wctomb.o(.constdata) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_longlong_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_longlong_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_oct_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) refers (Weak) to _printf_char.o(.text) for _printf_char
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) refers (Weak) to _printf_charcount.o(.text) for _printf_charcount
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_int_hex
    _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_hex_ptr
    _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_int_oct
    _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) refers (Weak) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Weak) to _printf_wchar.o(.text) for _printf_wchar
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Weak) to _printf_wchar.o(.text) for _printf_wstring
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_ll_oct
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_ll_hex
    _strtoul.o(.text) refers to _chval.o(.text) for _chval
    _strtoul.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    strtof.o(.text) refers to strtod.o(.text) for __strtod_int
    deqf.o(x$fpl$deqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    deqf.o(x$fpl$deqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    deqf.o(x$fpl$deqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dgeqf.o(x$fpl$dgeqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dgeqf.o(x$fpl$dgeqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dgeqf.o(x$fpl$dgeqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dleqf.o(x$fpl$dleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dleqf.o(x$fpl$dleqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drleqf.o(x$fpl$drleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drleqf.o(x$fpl$drleqf) refers to dleqf.o(x$fpl$dleqf) for __fpl_dcmple_InfNaN
    drnd.o(x$fpl$drnd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drnd.o(x$fpl$drnd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    feqf.o(x$fpl$feqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    feqf.o(x$fpl$feqf) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    feqf.o(x$fpl$feqf) refers to fcmpi.o(x$fpl$fcmpinf) for __fpl_fcmp_Inf
    fgeqf.o(x$fpl$fgeqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fgeqf.o(x$fpl$fgeqf) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fgeqf.o(x$fpl$fgeqf) refers to fcmpi.o(x$fpl$fcmpinf) for __fpl_fcmp_Inf
    fleqf.o(x$fpl$fleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fleqf.o(x$fpl$fleqf) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fleqf.o(x$fpl$fleqf) refers to fcmpi.o(x$fpl$fcmpinf) for __fpl_fcmp_Inf
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    printf2.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    printf2b.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    atan.o(i.__softfp_atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.__softfp_atan) refers to atan.o(i.atan) for atan
    atan.o(i.atan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan.o(i.atan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan.o(i.atan) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    atan.o(i.atan) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    atan.o(i.atan) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    atan.o(i.atan) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    atan.o(i.atan) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    atan.o(i.atan) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    atan.o(i.atan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    atan.o(i.atan) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan.o(i.atan) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    atan.o(i.atan) refers to atan.o(.constdata) for .constdata
    atan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_infnan2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    qnan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(.text.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_fp_hex.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_hex.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_hex.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers to _printf_fp_hex.o(.constdata) for .constdata
    _printf_fp_hex.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_wchar.o(.text) refers (Weak) to _printf_wctomb.o(.text) for _printf_wctomb
    strtod.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    strtod.o(.text) refers to scanf1.o(x$fpl$scanf1) for _scanf_real
    strtod.o(.text) refers to _sgetc.o(.text) for _sgetc
    strtod.o(.text) refers to isspace.o(.text) for isspace
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod_accurate.o(.text) refers to btod_accurate_common.o(.text) for _btod_common
    _c16rtomb.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    lc_ctype_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000014) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000014) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_ctype_c.o(locale$$code) refers to lc_ctype_c.o(locale$$data) for __lcctype_c_name
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000018) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000018) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    dcmpi.o(x$fpl$dcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fcmpi.o(x$fpl$fcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbn.o(x$fpl$scalbn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbn.o(x$fpl$scalbn) refers to dcheck1.o(x$fpl$dcheck1) for __fpl_dcheck_NaN1
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f10x_md.o(.text) for __user_initial_stackheap
    isspace.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    btod_accurate_common.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000034) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000006) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000010) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_relocate_pie_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000035) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000027) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_user_alloc_1
    libinit2.o(.ARM.Collect$$libinit$$00000011) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers (Weak) to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$0000001A) refers to libinit2.o(.ARM.Collect$$libinit$$00000011) for .ARM.Collect$$libinit$$00000011
    libinit2.o(.ARM.Collect$$libinit$$00000028) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000029) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    dcheck1.o(x$fpl$dcheck1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcheck1.o(x$fpl$dcheck1) refers to retnan.o(x$fpl$retnan) for __fpl_return_NaN
    scanf1.o(x$fpl$scanf1) refers to scanf_fp.o(.text) for _scanf_really_real
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    scanf_fp.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scanf_fp.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    scanf_fp.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    scanf_fp.o(.text) refers to istatus.o(x$fpl$ieeestatus) for __ieee_status
    scanf_fp.o(.text) refers to bigflt0.o(.text) for _btod_etento
    scanf_fp.o(.text) refers to btod.o(CL$$btod_emuld) for _btod_emuld
    scanf_fp.o(.text) refers to btod.o(CL$$btod_edivd) for _btod_edivd
    scanf_fp.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    scanf_fp.o(.text) refers to scanf2.o(x$fpl$scanf2) for _scanf_infnan
    scanf_fp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    scanf_fp.o(.text) refers to fpconst.o(c$$dmax) for __dbl_max
    scanf_fp.o(.text) refers to fpconst.o(c$$dinf) for __huge_val
    scanf_fp.o(.text) refers to narrow.o(i.__mathlib_narrow) for __mathlib_narrow
    scanf_fp_accurate.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scanf_fp_accurate.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    scanf_fp_accurate.o(.text) refers to strlen.o(.text) for strlen
    scanf_fp_accurate.o(.text) refers to dtob_accurate.o(.text) for _dtob
    scanf_fp_accurate.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    scanf_fp_accurate.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    scanf_fp_accurate.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    scanf_fp_accurate.o(.text) refers to scanf2.o(x$fpl$scanf2) for _scanf_infnan
    scanf_fp_accurate.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    scanf_fp_accurate.o(.text) refers to narrow.o(i.__mathlib_narrow) for __mathlib_narrow
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    retnan.o(x$fpl$retnan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(x$fpl$retnan) refers to trapv.o(x$fpl$trapveneer) for __fpl_cmpreturn
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    dtob_accurate.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dtob_accurate.o(.text) refers to btod_accurate_common.o(.text) for _btod_pow
    dtob_accurate.o(.text) refers to dtob_accurate.o(.constdata) for .constdata
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    fpconst.o(c$$dinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpconst.o(c$$dnan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpconst.o(c$$finf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpconst.o(c$$dmax) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scanf2.o(x$fpl$scanf2) refers to scanf_hexfp.o(.text) for _scanf_really_hex_real
    scanf2.o(x$fpl$scanf2) refers to scanf_infnan.o(.text) for _scanf_really_infnan
    scanf2b.o(x$fpl$scanf2) refers to scanf_hexfp.o(.text) for _scanf_really_hex_real
    scanf2b.o(x$fpl$scanf2) refers to scanf_infnan.o(.text) for _scanf_really_infnan
    trapv.o(x$fpl$trapveneer) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__mathlib_narrow) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__mathlib_narrow) refers to narrow.o(i.__mathlib_tofloat) for __mathlib_tofloat
    narrow.o(i.__mathlib_tofloat) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__mathlib_tofloat) refers to frexp.o(i.frexp) for frexp
    narrow.o(i.__mathlib_tofloat) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    narrow.o(i.__mathlib_tofloat) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    narrow.o(i.__mathlib_tofloat) refers to _rserrno.o(.text) for __set_errno
    narrow.o(i.__mathlib_tofloat) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    narrow.o(i.__softfp___mathlib_tofloat) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    narrow.o(i.__softfp___mathlib_tofloat) refers to narrow.o(i.__mathlib_tofloat) for __mathlib_tofloat
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_command_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_exit_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    scanf_hexfp.o(.text) refers to _chval.o(.text) for _chval
    scanf_hexfp.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    scanf_hexfp.o(.text) refers to ldexp.o(i.__support_ldexp) for __support_ldexp
    scanf_hexfp.o(.text) refers to narrow.o(i.__mathlib_narrow) for __mathlib_narrow
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    frexp.o(i.__softfp_frexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frexp.o(i.__softfp_frexp) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    frexp.o(i.frexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frexp.o(i.frexp) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    ldexp.o(i.__softfp_ldexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.__softfp_ldexp) refers to ldexp.o(i.ldexp) for ldexp
    ldexp.o(i.__support_ldexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.__support_ldexp) refers to ldexp.o(i.ldexp) for ldexp
    ldexp.o(i.ldexp) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ldexp.o(i.ldexp) refers to deqf.o(x$fpl$deqf) for __aeabi_cdcmpeq
    ldexp.o(i.ldexp) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    ldexp.o(i.ldexp) refers to _rserrno.o(.text) for __set_errno
    ldexp.o(i.ldexp) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    ldexp.o(i.ldexp) refers to dunder.o(i.__mathlib_dbl_overflow) for __mathlib_dbl_overflow
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_wrch_hlt.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch_hlt.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing stm32f10x_adc.o(.text), (0 bytes).
    Removing stm32f10x_adc.o(.text.ADC_DeInit), (76 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_DeInit), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_Init), (82 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_Init), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_StructInit), (16 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_StructInit), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_Cmd), (16 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_Cmd), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_DMACmd), (18 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_DMACmd), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_ITConfig), (18 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_ITConfig), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_ResetCalibration), (10 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_ResetCalibration), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_GetResetCalibrationStatus), (8 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_GetResetCalibrationStatus), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_StartCalibration), (10 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_StartCalibration), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_GetCalibrationStatus), (8 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_GetCalibrationStatus), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_SoftwareStartConvCmd), (18 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_SoftwareStartConvCmd), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_GetSoftwareStartConvStatus), (8 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_GetSoftwareStartConvStatus), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_DiscModeChannelCountConfig), (24 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_DiscModeChannelCountConfig), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_DiscModeCmd), (18 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_DiscModeCmd), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_RegularChannelConfig), (96 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_RegularChannelConfig), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_ExternalTrigConvCmd), (18 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_ExternalTrigConvCmd), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_GetConversionValue), (6 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_GetConversionValue), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_GetDualModeConversionValue), (12 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_GetDualModeConversionValue), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_AutoInjectedConvCmd), (18 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_AutoInjectedConvCmd), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_InjectedDiscModeCmd), (18 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_InjectedDiscModeCmd), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_ExternalTrigInjectedConvConfig), (12 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_ExternalTrigInjectedConvConfig), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_ExternalTrigInjectedConvCmd), (18 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_ExternalTrigInjectedConvCmd), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_SoftwareStartInjectedConvCmd), (18 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_SoftwareStartInjectedConvCmd), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_GetSoftwareStartInjectedConvCmdStatus), (8 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_GetSoftwareStartInjectedConvCmdStatus), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_InjectedChannelConfig), (78 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_InjectedChannelConfig), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_InjectedSequencerLengthConfig), (22 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_InjectedSequencerLengthConfig), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_SetInjectedOffset), (22 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_SetInjectedOffset), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_GetInjectedConversionValue), (26 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_GetInjectedConversionValue), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_AnalogWatchdogCmd), (18 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_AnalogWatchdogCmd), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_AnalogWatchdogThresholdsConfig), (6 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_AnalogWatchdogThresholdsConfig), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_AnalogWatchdogSingleChannelConfig), (12 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_AnalogWatchdogSingleChannelConfig), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_TempSensorVrefintCmd), (26 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_TempSensorVrefintCmd), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_GetFlagStatus), (10 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_GetFlagStatus), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_ClearFlag), (6 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_ClearFlag), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_GetITStatus), (26 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_GetITStatus), (8 bytes).
    Removing stm32f10x_adc.o(.text.ADC_ClearITPendingBit), (8 bytes).
    Removing stm32f10x_adc.o(.ARM.exidx.text.ADC_ClearITPendingBit), (8 bytes).
    Removing misc.o(.text), (0 bytes).
    Removing misc.o(.text.NVIC_PriorityGroupConfig), (20 bytes).
    Removing misc.o(.ARM.exidx.text.NVIC_PriorityGroupConfig), (8 bytes).
    Removing misc.o(.ARM.exidx.text.NVIC_Init), (8 bytes).
    Removing misc.o(.text.NVIC_SetVectorTable), (24 bytes).
    Removing misc.o(.ARM.exidx.text.NVIC_SetVectorTable), (8 bytes).
    Removing misc.o(.text.NVIC_SystemLPConfig), (24 bytes).
    Removing misc.o(.ARM.exidx.text.NVIC_SystemLPConfig), (8 bytes).
    Removing misc.o(.text.SysTick_CLKSourceConfig), (24 bytes).
    Removing misc.o(.ARM.exidx.text.SysTick_CLKSourceConfig), (8 bytes).
    Removing stm32f10x_bkp.o(.text), (0 bytes).
    Removing stm32f10x_bkp.o(.text.BKP_DeInit), (18 bytes).
    Removing stm32f10x_bkp.o(.ARM.exidx.text.BKP_DeInit), (8 bytes).
    Removing stm32f10x_bkp.o(.text.BKP_TamperPinLevelConfig), (12 bytes).
    Removing stm32f10x_bkp.o(.ARM.exidx.text.BKP_TamperPinLevelConfig), (8 bytes).
    Removing stm32f10x_bkp.o(.text.BKP_TamperPinCmd), (12 bytes).
    Removing stm32f10x_bkp.o(.ARM.exidx.text.BKP_TamperPinCmd), (8 bytes).
    Removing stm32f10x_bkp.o(.text.BKP_ITConfig), (12 bytes).
    Removing stm32f10x_bkp.o(.ARM.exidx.text.BKP_ITConfig), (8 bytes).
    Removing stm32f10x_bkp.o(.text.BKP_RTCOutputConfig), (20 bytes).
    Removing stm32f10x_bkp.o(.ARM.exidx.text.BKP_RTCOutputConfig), (8 bytes).
    Removing stm32f10x_bkp.o(.text.BKP_SetRTCCalibrationValue), (20 bytes).
    Removing stm32f10x_bkp.o(.ARM.exidx.text.BKP_SetRTCCalibrationValue), (8 bytes).
    Removing stm32f10x_bkp.o(.text.BKP_WriteBackupRegister), (30 bytes).
    Removing stm32f10x_bkp.o(.ARM.exidx.text.BKP_WriteBackupRegister), (8 bytes).
    Removing stm32f10x_bkp.o(.text.BKP_ReadBackupRegister), (30 bytes).
    Removing stm32f10x_bkp.o(.ARM.exidx.text.BKP_ReadBackupRegister), (8 bytes).
    Removing stm32f10x_bkp.o(.text.BKP_GetFlagStatus), (14 bytes).
    Removing stm32f10x_bkp.o(.ARM.exidx.text.BKP_GetFlagStatus), (8 bytes).
    Removing stm32f10x_bkp.o(.text.BKP_ClearFlag), (18 bytes).
    Removing stm32f10x_bkp.o(.ARM.exidx.text.BKP_ClearFlag), (8 bytes).
    Removing stm32f10x_bkp.o(.text.BKP_GetITStatus), (14 bytes).
    Removing stm32f10x_bkp.o(.ARM.exidx.text.BKP_GetITStatus), (8 bytes).
    Removing stm32f10x_bkp.o(.text.BKP_ClearITPendingBit), (18 bytes).
    Removing stm32f10x_bkp.o(.ARM.exidx.text.BKP_ClearITPendingBit), (8 bytes).
    Removing stm32f10x_can.o(.text), (0 bytes).
    Removing stm32f10x_can.o(.text.CAN_DeInit), (42 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_DeInit), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_Init), (228 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_Init), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_FilterInit), (172 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_FilterInit), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_StructInit), (20 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_StructInit), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_SlaveStartBank), (44 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_SlaveStartBank), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_DBGFreeze), (18 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_DBGFreeze), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_TTComModeCmd), (92 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_TTComModeCmd), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_Transmit), (160 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_Transmit), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_TransmitStatus), (128 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_TransmitStatus), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_CancelTransmit), (32 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_CancelTransmit), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_Receive), (160 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_Receive), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_FIFORelease), (18 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_FIFORelease), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_MessagePending), (24 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_MessagePending), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_OperatingModeRequest), (146 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_OperatingModeRequest), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_Sleep), (26 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_Sleep), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_WakeUp), (44 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_WakeUp), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_GetLastErrorCode), (8 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_GetLastErrorCode), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_GetReceiveErrorCounter), (6 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_GetReceiveErrorCounter), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_GetLSBTransmitErrorCounter), (8 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_GetLSBTransmitErrorCounter), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_ITConfig), (16 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_ITConfig), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_GetFlagStatus), (52 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_GetFlagStatus), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_ClearFlag), (58 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_ClearFlag), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_GetITStatus), (280 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_GetITStatus), (8 bytes).
    Removing stm32f10x_can.o(.text.CAN_ClearITPendingBit), (172 bytes).
    Removing stm32f10x_can.o(.ARM.exidx.text.CAN_ClearITPendingBit), (8 bytes).
    Removing stm32f10x_cec.o(.text), (0 bytes).
    Removing stm32f10x_cec.o(.text.CEC_DeInit), (26 bytes).
    Removing stm32f10x_cec.o(.ARM.exidx.text.CEC_DeInit), (8 bytes).
    Removing stm32f10x_cec.o(.text.CEC_Init), (28 bytes).
    Removing stm32f10x_cec.o(.ARM.exidx.text.CEC_Init), (8 bytes).
    Removing stm32f10x_cec.o(.text.CEC_Cmd), (32 bytes).
    Removing stm32f10x_cec.o(.ARM.exidx.text.CEC_Cmd), (8 bytes).
    Removing stm32f10x_cec.o(.text.CEC_ITConfig), (10 bytes).
    Removing stm32f10x_cec.o(.ARM.exidx.text.CEC_ITConfig), (8 bytes).
    Removing stm32f10x_cec.o(.text.CEC_OwnAddressConfig), (12 bytes).
    Removing stm32f10x_cec.o(.ARM.exidx.text.CEC_OwnAddressConfig), (8 bytes).
    Removing stm32f10x_cec.o(.text.CEC_SetPrescaler), (12 bytes).
    Removing stm32f10x_cec.o(.ARM.exidx.text.CEC_SetPrescaler), (8 bytes).
    Removing stm32f10x_cec.o(.text.CEC_SendDataByte), (12 bytes).
    Removing stm32f10x_cec.o(.ARM.exidx.text.CEC_SendDataByte), (8 bytes).
    Removing stm32f10x_cec.o(.text.CEC_ReceiveDataByte), (14 bytes).
    Removing stm32f10x_cec.o(.ARM.exidx.text.CEC_ReceiveDataByte), (8 bytes).
    Removing stm32f10x_cec.o(.text.CEC_StartOfMessage), (14 bytes).
    Removing stm32f10x_cec.o(.ARM.exidx.text.CEC_StartOfMessage), (8 bytes).
    Removing stm32f10x_cec.o(.text.CEC_EndOfMessageCmd), (12 bytes).
    Removing stm32f10x_cec.o(.ARM.exidx.text.CEC_EndOfMessageCmd), (8 bytes).
    Removing stm32f10x_cec.o(.text.CEC_GetFlagStatus), (34 bytes).
    Removing stm32f10x_cec.o(.ARM.exidx.text.CEC_GetFlagStatus), (8 bytes).
    Removing stm32f10x_cec.o(.text.CEC_ClearFlag), (34 bytes).
    Removing stm32f10x_cec.o(.ARM.exidx.text.CEC_ClearFlag), (8 bytes).
    Removing stm32f10x_cec.o(.text.CEC_GetITStatus), (28 bytes).
    Removing stm32f10x_cec.o(.ARM.exidx.text.CEC_GetITStatus), (8 bytes).
    Removing stm32f10x_cec.o(.text.CEC_ClearITPendingBit), (34 bytes).
    Removing stm32f10x_cec.o(.ARM.exidx.text.CEC_ClearITPendingBit), (8 bytes).
    Removing stm32f10x_crc.o(.text), (0 bytes).
    Removing stm32f10x_crc.o(.text.CRC_ResetDR), (14 bytes).
    Removing stm32f10x_crc.o(.ARM.exidx.text.CRC_ResetDR), (8 bytes).
    Removing stm32f10x_crc.o(.text.CRC_CalcCRC), (14 bytes).
    Removing stm32f10x_crc.o(.ARM.exidx.text.CRC_CalcCRC), (8 bytes).
    Removing stm32f10x_crc.o(.text.CRC_CalcBlockCRC), (26 bytes).
    Removing stm32f10x_crc.o(.ARM.exidx.text.CRC_CalcBlockCRC), (8 bytes).
    Removing stm32f10x_crc.o(.text.CRC_GetCRC), (12 bytes).
    Removing stm32f10x_crc.o(.ARM.exidx.text.CRC_GetCRC), (8 bytes).
    Removing stm32f10x_crc.o(.text.CRC_SetIDRegister), (12 bytes).
    Removing stm32f10x_crc.o(.ARM.exidx.text.CRC_SetIDRegister), (8 bytes).
    Removing stm32f10x_crc.o(.text.CRC_GetIDRegister), (12 bytes).
    Removing stm32f10x_crc.o(.ARM.exidx.text.CRC_GetIDRegister), (8 bytes).
    Removing stm32f10x_dac.o(.text), (0 bytes).
    Removing stm32f10x_dac.o(.text.DAC_DeInit), (26 bytes).
    Removing stm32f10x_dac.o(.ARM.exidx.text.DAC_DeInit), (8 bytes).
    Removing stm32f10x_dac.o(.text.DAC_Init), (52 bytes).
    Removing stm32f10x_dac.o(.ARM.exidx.text.DAC_Init), (8 bytes).
    Removing stm32f10x_dac.o(.text.DAC_StructInit), (12 bytes).
    Removing stm32f10x_dac.o(.ARM.exidx.text.DAC_StructInit), (8 bytes).
    Removing stm32f10x_dac.o(.text.DAC_Cmd), (30 bytes).
    Removing stm32f10x_dac.o(.ARM.exidx.text.DAC_Cmd), (8 bytes).
    Removing stm32f10x_dac.o(.text.DAC_DMACmd), (32 bytes).
    Removing stm32f10x_dac.o(.ARM.exidx.text.DAC_DMACmd), (8 bytes).
    Removing stm32f10x_dac.o(.text.DAC_SoftwareTriggerCmd), (32 bytes).
    Removing stm32f10x_dac.o(.ARM.exidx.text.DAC_SoftwareTriggerCmd), (8 bytes).
    Removing stm32f10x_dac.o(.text.DAC_DualSoftwareTriggerCmd), (24 bytes).
    Removing stm32f10x_dac.o(.ARM.exidx.text.DAC_DualSoftwareTriggerCmd), (8 bytes).
    Removing stm32f10x_dac.o(.text.DAC_WaveGenerationCmd), (28 bytes).
    Removing stm32f10x_dac.o(.ARM.exidx.text.DAC_WaveGenerationCmd), (8 bytes).
    Removing stm32f10x_dac.o(.text.DAC_SetChannel1Data), (32 bytes).
    Removing stm32f10x_dac.o(.ARM.exidx.text.DAC_SetChannel1Data), (8 bytes).
    Removing stm32f10x_dac.o(.text.DAC_SetChannel2Data), (32 bytes).
    Removing stm32f10x_dac.o(.ARM.exidx.text.DAC_SetChannel2Data), (8 bytes).
    Removing stm32f10x_dac.o(.text.DAC_SetDualChannelData), (24 bytes).
    Removing stm32f10x_dac.o(.ARM.exidx.text.DAC_SetDualChannelData), (8 bytes).
    Removing stm32f10x_dac.o(.text.DAC_GetDataOutputValue), (36 bytes).
    Removing stm32f10x_dac.o(.ARM.exidx.text.DAC_GetDataOutputValue), (8 bytes).
    Removing stm32f10x_dbgmcu.o(.text), (0 bytes).
    Removing stm32f10x_dbgmcu.o(.text.DBGMCU_GetREVID), (14 bytes).
    Removing stm32f10x_dbgmcu.o(.ARM.exidx.text.DBGMCU_GetREVID), (8 bytes).
    Removing stm32f10x_dbgmcu.o(.text.DBGMCU_GetDEVID), (16 bytes).
    Removing stm32f10x_dbgmcu.o(.ARM.exidx.text.DBGMCU_GetDEVID), (8 bytes).
    Removing stm32f10x_dbgmcu.o(.text.DBGMCU_Config), (24 bytes).
    Removing stm32f10x_dbgmcu.o(.ARM.exidx.text.DBGMCU_Config), (8 bytes).
    Removing stm32f10x_dma.o(.text), (0 bytes).
    Removing stm32f10x_dma.o(.text.DMA_DeInit), (268 bytes).
    Removing stm32f10x_dma.o(.ARM.exidx.text.DMA_DeInit), (8 bytes).
    Removing stm32f10x_dma.o(.text.DMA_Init), (72 bytes).
    Removing stm32f10x_dma.o(.ARM.exidx.text.DMA_Init), (8 bytes).
    Removing stm32f10x_dma.o(.text.DMA_StructInit), (10 bytes).
    Removing stm32f10x_dma.o(.ARM.exidx.text.DMA_StructInit), (8 bytes).
    Removing stm32f10x_dma.o(.text.DMA_Cmd), (22 bytes).
    Removing stm32f10x_dma.o(.ARM.exidx.text.DMA_Cmd), (8 bytes).
    Removing stm32f10x_dma.o(.text.DMA_ITConfig), (16 bytes).
    Removing stm32f10x_dma.o(.ARM.exidx.text.DMA_ITConfig), (8 bytes).
    Removing stm32f10x_dma.o(.text.DMA_SetCurrDataCounter), (4 bytes).
    Removing stm32f10x_dma.o(.ARM.exidx.text.DMA_SetCurrDataCounter), (8 bytes).
    Removing stm32f10x_dma.o(.text.DMA_GetCurrDataCounter), (6 bytes).
    Removing stm32f10x_dma.o(.ARM.exidx.text.DMA_GetCurrDataCounter), (8 bytes).
    Removing stm32f10x_dma.o(.text.DMA_GetFlagStatus), (22 bytes).
    Removing stm32f10x_dma.o(.ARM.exidx.text.DMA_GetFlagStatus), (8 bytes).
    Removing stm32f10x_dma.o(.text.DMA_ClearFlag), (16 bytes).
    Removing stm32f10x_dma.o(.ARM.exidx.text.DMA_ClearFlag), (8 bytes).
    Removing stm32f10x_dma.o(.text.DMA_GetITStatus), (22 bytes).
    Removing stm32f10x_dma.o(.ARM.exidx.text.DMA_GetITStatus), (8 bytes).
    Removing stm32f10x_dma.o(.text.DMA_ClearITPendingBit), (16 bytes).
    Removing stm32f10x_dma.o(.ARM.exidx.text.DMA_ClearITPendingBit), (8 bytes).
    Removing stm32f10x_exti.o(.text), (0 bytes).
    Removing stm32f10x_exti.o(.text.EXTI_DeInit), (30 bytes).
    Removing stm32f10x_exti.o(.ARM.exidx.text.EXTI_DeInit), (8 bytes).
    Removing stm32f10x_exti.o(.ARM.exidx.text.EXTI_Init), (8 bytes).
    Removing stm32f10x_exti.o(.text.EXTI_StructInit), (14 bytes).
    Removing stm32f10x_exti.o(.ARM.exidx.text.EXTI_StructInit), (8 bytes).
    Removing stm32f10x_exti.o(.text.EXTI_GenerateSWInterrupt), (16 bytes).
    Removing stm32f10x_exti.o(.ARM.exidx.text.EXTI_GenerateSWInterrupt), (8 bytes).
    Removing stm32f10x_exti.o(.text.EXTI_GetFlagStatus), (18 bytes).
    Removing stm32f10x_exti.o(.ARM.exidx.text.EXTI_GetFlagStatus), (8 bytes).
    Removing stm32f10x_exti.o(.text.EXTI_ClearFlag), (12 bytes).
    Removing stm32f10x_exti.o(.ARM.exidx.text.EXTI_ClearFlag), (8 bytes).
    Removing stm32f10x_exti.o(.ARM.exidx.text.EXTI_GetITStatus), (8 bytes).
    Removing stm32f10x_exti.o(.ARM.exidx.text.EXTI_ClearITPendingBit), (8 bytes).
    Removing stm32f10x_flash.o(.text), (0 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_SetLatency), (20 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_SetLatency), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_HalfCycleAccessCmd), (24 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_HalfCycleAccessCmd), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_PrefetchBufferCmd), (24 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_PrefetchBufferCmd), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_Unlock), (30 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_Unlock), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_UnlockBank1), (30 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_UnlockBank1), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_Lock), (18 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_Lock), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_LockBank1), (18 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_LockBank1), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_ErasePage), (254 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_ErasePage), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_WaitForLastOperation), (118 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_WaitForLastOperation), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_EraseAllPages), (246 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_EraseAllPages), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_EraseAllBank1Pages), (246 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_EraseAllBank1Pages), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_WaitForLastBank1Operation), (118 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_WaitForLastBank1Operation), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_EraseOptionBytes), (436 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_EraseOptionBytes), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_GetReadOutProtectionStatus), (16 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_GetReadOutProtectionStatus), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_ProgramWord), (492 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_ProgramWord), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_ProgramHalfWord), (278 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_ProgramHalfWord), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_ProgramOptionByteData), (312 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_ProgramOptionByteData), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_EnableWriteProtection), (664 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_EnableWriteProtection), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_ReadOutProtection), (440 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_ReadOutProtection), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_UserOptionByteConfig), (328 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_UserOptionByteConfig), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_GetUserOptionByte), (14 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_GetUserOptionByte), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_GetWriteProtectionOptionByte), (12 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_GetWriteProtectionOptionByte), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_GetPrefetchBufferStatus), (16 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_GetPrefetchBufferStatus), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_ITConfig), (24 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_ITConfig), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_GetFlagStatus), (30 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_GetFlagStatus), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_ClearFlag), (12 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_ClearFlag), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_GetStatus), (42 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_GetStatus), (8 bytes).
    Removing stm32f10x_flash.o(.text.FLASH_GetBank1Status), (42 bytes).
    Removing stm32f10x_flash.o(.ARM.exidx.text.FLASH_GetBank1Status), (8 bytes).
    Removing stm32f10x_fsmc.o(.text), (0 bytes).
    Removing stm32f10x_fsmc.o(.text.FSMC_NORSRAMDeInit), (50 bytes).
    Removing stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NORSRAMDeInit), (8 bytes).
    Removing stm32f10x_fsmc.o(.text.FSMC_NANDDeInit), (56 bytes).
    Removing stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NANDDeInit), (8 bytes).
    Removing stm32f10x_fsmc.o(.text.FSMC_PCCARDDeInit), (26 bytes).
    Removing stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_PCCARDDeInit), (8 bytes).
    Removing stm32f10x_fsmc.o(.text.FSMC_NORSRAMInit), (192 bytes).
    Removing stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NORSRAMInit), (8 bytes).
    Removing stm32f10x_fsmc.o(.text.FSMC_NANDInit), (122 bytes).
    Removing stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NANDInit), (8 bytes).
    Removing stm32f10x_fsmc.o(.text.FSMC_PCCARDInit), (110 bytes).
    Removing stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_PCCARDInit), (8 bytes).
    Removing stm32f10x_fsmc.o(.text.FSMC_NORSRAMStructInit), (80 bytes).
    Removing stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NORSRAMStructInit), (8 bytes).
    Removing stm32f10x_fsmc.o(.text.FSMC_NANDStructInit), (42 bytes).
    Removing stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NANDStructInit), (8 bytes).
    Removing stm32f10x_fsmc.o(.text.FSMC_PCCARDStructInit), (42 bytes).
    Removing stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_PCCARDStructInit), (8 bytes).
    Removing stm32f10x_fsmc.o(.text.FSMC_NORSRAMCmd), (36 bytes).
    Removing stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NORSRAMCmd), (8 bytes).
    Removing stm32f10x_fsmc.o(.text.FSMC_NANDCmd), (64 bytes).
    Removing stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NANDCmd), (8 bytes).
    Removing stm32f10x_fsmc.o(.text.FSMC_PCCARDCmd), (32 bytes).
    Removing stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_PCCARDCmd), (8 bytes).
    Removing stm32f10x_fsmc.o(.text.FSMC_NANDECCCmd), (64 bytes).
    Removing stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_NANDECCCmd), (8 bytes).
    Removing stm32f10x_fsmc.o(.text.FSMC_GetECC), (22 bytes).
    Removing stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_GetECC), (8 bytes).
    Removing stm32f10x_fsmc.o(.text.FSMC_ITConfig), (84 bytes).
    Removing stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_ITConfig), (8 bytes).
    Removing stm32f10x_fsmc.o(.text.FSMC_GetFlagStatus), (30 bytes).
    Removing stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_GetFlagStatus), (8 bytes).
    Removing stm32f10x_fsmc.o(.text.FSMC_ClearFlag), (28 bytes).
    Removing stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_ClearFlag), (8 bytes).
    Removing stm32f10x_fsmc.o(.text.FSMC_GetITStatus), (44 bytes).
    Removing stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_GetITStatus), (8 bytes).
    Removing stm32f10x_fsmc.o(.text.FSMC_ClearITPendingBit), (30 bytes).
    Removing stm32f10x_fsmc.o(.ARM.exidx.text.FSMC_ClearITPendingBit), (8 bytes).
    Removing stm32f10x_gpio.o(.text), (0 bytes).
    Removing stm32f10x_gpio.o(.text.GPIO_DeInit), (52 bytes).
    Removing stm32f10x_gpio.o(.ARM.exidx.text.GPIO_DeInit), (8 bytes).
    Removing stm32f10x_gpio.o(.text.GPIO_AFIODeInit), (22 bytes).
    Removing stm32f10x_gpio.o(.ARM.exidx.text.GPIO_AFIODeInit), (8 bytes).
    Removing stm32f10x_gpio.o(.ARM.exidx.text.GPIO_Init), (8 bytes).
    Removing stm32f10x_gpio.o(.text.GPIO_StructInit), (12 bytes).
    Removing stm32f10x_gpio.o(.ARM.exidx.text.GPIO_StructInit), (8 bytes).
    Removing stm32f10x_gpio.o(.ARM.exidx.text.GPIO_ReadInputDataBit), (8 bytes).
    Removing stm32f10x_gpio.o(.text.GPIO_ReadInputData), (6 bytes).
    Removing stm32f10x_gpio.o(.ARM.exidx.text.GPIO_ReadInputData), (8 bytes).
    Removing stm32f10x_gpio.o(.text.GPIO_ReadOutputDataBit), (10 bytes).
    Removing stm32f10x_gpio.o(.ARM.exidx.text.GPIO_ReadOutputDataBit), (8 bytes).
    Removing stm32f10x_gpio.o(.text.GPIO_ReadOutputData), (6 bytes).
    Removing stm32f10x_gpio.o(.ARM.exidx.text.GPIO_ReadOutputData), (8 bytes).
    Removing stm32f10x_gpio.o(.text.GPIO_SetBits), (4 bytes).
    Removing stm32f10x_gpio.o(.ARM.exidx.text.GPIO_SetBits), (8 bytes).
    Removing stm32f10x_gpio.o(.text.GPIO_ResetBits), (4 bytes).
    Removing stm32f10x_gpio.o(.ARM.exidx.text.GPIO_ResetBits), (8 bytes).
    Removing stm32f10x_gpio.o(.ARM.exidx.text.GPIO_WriteBit), (8 bytes).
    Removing stm32f10x_gpio.o(.text.GPIO_Write), (4 bytes).
    Removing stm32f10x_gpio.o(.ARM.exidx.text.GPIO_Write), (8 bytes).
    Removing stm32f10x_gpio.o(.text.GPIO_PinLockConfig), (16 bytes).
    Removing stm32f10x_gpio.o(.ARM.exidx.text.GPIO_PinLockConfig), (8 bytes).
    Removing stm32f10x_gpio.o(.text.GPIO_EventOutputConfig), (30 bytes).
    Removing stm32f10x_gpio.o(.ARM.exidx.text.GPIO_EventOutputConfig), (8 bytes).
    Removing stm32f10x_gpio.o(.text.GPIO_EventOutputCmd), (10 bytes).
    Removing stm32f10x_gpio.o(.ARM.exidx.text.GPIO_EventOutputCmd), (8 bytes).
    Removing stm32f10x_gpio.o(.text.GPIO_PinRemapConfig), (128 bytes).
    Removing stm32f10x_gpio.o(.ARM.exidx.text.GPIO_PinRemapConfig), (8 bytes).
    Removing stm32f10x_gpio.o(.ARM.exidx.text.GPIO_EXTILineConfig), (8 bytes).
    Removing stm32f10x_gpio.o(.text.GPIO_ETH_MediaInterfaceConfig), (10 bytes).
    Removing stm32f10x_gpio.o(.ARM.exidx.text.GPIO_ETH_MediaInterfaceConfig), (8 bytes).
    Removing stm32f10x_gpio.o(.rodata..Lswitch.table.GPIO_DeInit.1), (28 bytes).
    Removing stm32f10x_i2c.o(.text), (0 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_DeInit), (42 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_DeInit), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_Init), (202 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_Init), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_StructInit), (24 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_StructInit), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_Cmd), (18 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_Cmd), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_DMACmd), (20 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_DMACmd), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_DMALastTransferCmd), (20 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_DMALastTransferCmd), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_GenerateSTART), (20 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_GenerateSTART), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_GenerateSTOP), (20 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_GenerateSTOP), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_AcknowledgeConfig), (20 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_AcknowledgeConfig), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_OwnAddress2Config), (12 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_OwnAddress2Config), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_DualAddressCmd), (18 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_DualAddressCmd), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_GeneralCallCmd), (20 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_GeneralCallCmd), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_ITConfig), (16 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_ITConfig), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_SendData), (4 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_SendData), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_ReceiveData), (6 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_ReceiveData), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_Send7bitAddress), (16 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_Send7bitAddress), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_ReadRegister), (22 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_ReadRegister), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_SoftwareResetCmd), (20 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_SoftwareResetCmd), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_NACKPositionConfig), (24 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_NACKPositionConfig), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_SMBusAlertConfig), (24 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_SMBusAlertConfig), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_TransmitPEC), (20 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_TransmitPEC), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_PECPositionConfig), (24 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_PECPositionConfig), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_CalculatePEC), (20 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_CalculatePEC), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_GetPEC), (6 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_GetPEC), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_ARPCmd), (20 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_ARPCmd), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_StretchClockCmd), (20 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_StretchClockCmd), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_FastModeDutyCycleConfig), (24 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_FastModeDutyCycleConfig), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_CheckEvent), (22 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_CheckEvent), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_GetLastEvent), (12 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_GetLastEvent), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_GetFlagStatus), (48 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_GetFlagStatus), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_ClearFlag), (6 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_ClearFlag), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_GetITStatus), (28 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_GetITStatus), (8 bytes).
    Removing stm32f10x_i2c.o(.text.I2C_ClearITPendingBit), (6 bytes).
    Removing stm32f10x_i2c.o(.ARM.exidx.text.I2C_ClearITPendingBit), (8 bytes).
    Removing stm32f10x_iwdg.o(.text), (0 bytes).
    Removing stm32f10x_iwdg.o(.text.IWDG_WriteAccessCmd), (12 bytes).
    Removing stm32f10x_iwdg.o(.ARM.exidx.text.IWDG_WriteAccessCmd), (8 bytes).
    Removing stm32f10x_iwdg.o(.text.IWDG_SetPrescaler), (12 bytes).
    Removing stm32f10x_iwdg.o(.ARM.exidx.text.IWDG_SetPrescaler), (8 bytes).
    Removing stm32f10x_iwdg.o(.text.IWDG_SetReload), (12 bytes).
    Removing stm32f10x_iwdg.o(.ARM.exidx.text.IWDG_SetReload), (8 bytes).
    Removing stm32f10x_iwdg.o(.text.IWDG_ReloadCounter), (16 bytes).
    Removing stm32f10x_iwdg.o(.ARM.exidx.text.IWDG_ReloadCounter), (8 bytes).
    Removing stm32f10x_iwdg.o(.text.IWDG_Enable), (16 bytes).
    Removing stm32f10x_iwdg.o(.ARM.exidx.text.IWDG_Enable), (8 bytes).
    Removing stm32f10x_iwdg.o(.text.IWDG_GetFlagStatus), (18 bytes).
    Removing stm32f10x_iwdg.o(.ARM.exidx.text.IWDG_GetFlagStatus), (8 bytes).
    Removing stm32f10x_pwr.o(.text), (0 bytes).
    Removing stm32f10x_pwr.o(.text.PWR_DeInit), (26 bytes).
    Removing stm32f10x_pwr.o(.ARM.exidx.text.PWR_DeInit), (8 bytes).
    Removing stm32f10x_pwr.o(.text.PWR_BackupAccessCmd), (10 bytes).
    Removing stm32f10x_pwr.o(.ARM.exidx.text.PWR_BackupAccessCmd), (8 bytes).
    Removing stm32f10x_pwr.o(.text.PWR_PVDCmd), (10 bytes).
    Removing stm32f10x_pwr.o(.ARM.exidx.text.PWR_PVDCmd), (8 bytes).
    Removing stm32f10x_pwr.o(.text.PWR_PVDLevelConfig), (20 bytes).
    Removing stm32f10x_pwr.o(.ARM.exidx.text.PWR_PVDLevelConfig), (8 bytes).
    Removing stm32f10x_pwr.o(.text.PWR_WakeUpPinCmd), (10 bytes).
    Removing stm32f10x_pwr.o(.ARM.exidx.text.PWR_WakeUpPinCmd), (8 bytes).
    Removing stm32f10x_pwr.o(.text.PWR_EnterSTOPMode), (58 bytes).
    Removing stm32f10x_pwr.o(.ARM.exidx.text.PWR_EnterSTOPMode), (8 bytes).
    Removing stm32f10x_pwr.o(.text.PWR_EnterSTANDBYMode), (44 bytes).
    Removing stm32f10x_pwr.o(.ARM.exidx.text.PWR_EnterSTANDBYMode), (8 bytes).
    Removing stm32f10x_pwr.o(.text.PWR_GetFlagStatus), (18 bytes).
    Removing stm32f10x_pwr.o(.ARM.exidx.text.PWR_GetFlagStatus), (8 bytes).
    Removing stm32f10x_pwr.o(.text.PWR_ClearFlag), (18 bytes).
    Removing stm32f10x_pwr.o(.ARM.exidx.text.PWR_ClearFlag), (8 bytes).
    Removing stm32f10x_rcc.o(.text), (0 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_DeInit), (66 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_DeInit), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_HSEConfig), (50 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_HSEConfig), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_WaitForHSEStartUp), (48 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_WaitForHSEStartUp), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_GetFlagStatus), (38 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_GetFlagStatus), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_AdjustHSICalibrationValue), (22 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_AdjustHSICalibrationValue), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_HSICmd), (10 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_HSICmd), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_PLLConfig), (22 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_PLLConfig), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_PLLCmd), (10 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_PLLCmd), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_SYSCLKConfig), (20 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_SYSCLKConfig), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_GetSYSCLKSource), (16 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_GetSYSCLKSource), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_HCLKConfig), (20 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_HCLKConfig), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_PCLK1Config), (20 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_PCLK1Config), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_PCLK2Config), (22 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_PCLK2Config), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_ITConfig), (24 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_ITConfig), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_USBCLKConfig), (10 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_USBCLKConfig), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_ADCCLKConfig), (20 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_ADCCLKConfig), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_LSEConfig), (30 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_LSEConfig), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_LSICmd), (12 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_LSICmd), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_RTCCLKConfig), (16 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_RTCCLKConfig), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_RTCCLKCmd), (12 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_RTCCLKCmd), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_GetClocksFreq), (140 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_GetClocksFreq), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_AHBPeriphClockCmd), (24 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_AHBPeriphClockCmd), (8 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_APB2PeriphClockCmd), (8 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_APB1PeriphClockCmd), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_APB2PeriphResetCmd), (24 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_APB2PeriphResetCmd), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_APB1PeriphResetCmd), (24 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_APB1PeriphResetCmd), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_BackupResetCmd), (12 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_BackupResetCmd), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_ClockSecuritySystemCmd), (10 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_ClockSecuritySystemCmd), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_MCOConfig), (12 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_MCOConfig), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_ClearFlag), (18 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_ClearFlag), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_GetITStatus), (18 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_GetITStatus), (8 bytes).
    Removing stm32f10x_rcc.o(.text.RCC_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_rcc.o(.ARM.exidx.text.RCC_ClearITPendingBit), (8 bytes).
    Removing stm32f10x_rcc.o(.rodata.APBAHBPrescTable), (16 bytes).
    Removing stm32f10x_rcc.o(.rodata.ADCPrescTable), (4 bytes).
    Removing stm32f10x_rtc.o(.text), (0 bytes).
    Removing stm32f10x_rtc.o(.text.RTC_ITConfig), (24 bytes).
    Removing stm32f10x_rtc.o(.ARM.exidx.text.RTC_ITConfig), (8 bytes).
    Removing stm32f10x_rtc.o(.text.RTC_EnterConfigMode), (18 bytes).
    Removing stm32f10x_rtc.o(.ARM.exidx.text.RTC_EnterConfigMode), (8 bytes).
    Removing stm32f10x_rtc.o(.text.RTC_ExitConfigMode), (18 bytes).
    Removing stm32f10x_rtc.o(.ARM.exidx.text.RTC_ExitConfigMode), (8 bytes).
    Removing stm32f10x_rtc.o(.text.RTC_GetCounter), (18 bytes).
    Removing stm32f10x_rtc.o(.ARM.exidx.text.RTC_GetCounter), (8 bytes).
    Removing stm32f10x_rtc.o(.text.RTC_SetCounter), (32 bytes).
    Removing stm32f10x_rtc.o(.ARM.exidx.text.RTC_SetCounter), (8 bytes).
    Removing stm32f10x_rtc.o(.text.RTC_SetPrescaler), (34 bytes).
    Removing stm32f10x_rtc.o(.ARM.exidx.text.RTC_SetPrescaler), (8 bytes).
    Removing stm32f10x_rtc.o(.text.RTC_SetAlarm), (32 bytes).
    Removing stm32f10x_rtc.o(.ARM.exidx.text.RTC_SetAlarm), (8 bytes).
    Removing stm32f10x_rtc.o(.text.RTC_GetDivider), (22 bytes).
    Removing stm32f10x_rtc.o(.ARM.exidx.text.RTC_GetDivider), (8 bytes).
    Removing stm32f10x_rtc.o(.text.RTC_WaitForLastTask), (16 bytes).
    Removing stm32f10x_rtc.o(.ARM.exidx.text.RTC_WaitForLastTask), (8 bytes).
    Removing stm32f10x_rtc.o(.text.RTC_WaitForSynchro), (24 bytes).
    Removing stm32f10x_rtc.o(.ARM.exidx.text.RTC_WaitForSynchro), (8 bytes).
    Removing stm32f10x_rtc.o(.text.RTC_GetFlagStatus), (18 bytes).
    Removing stm32f10x_rtc.o(.ARM.exidx.text.RTC_GetFlagStatus), (8 bytes).
    Removing stm32f10x_rtc.o(.text.RTC_ClearFlag), (18 bytes).
    Removing stm32f10x_rtc.o(.ARM.exidx.text.RTC_ClearFlag), (8 bytes).
    Removing stm32f10x_rtc.o(.text.RTC_GetITStatus), (34 bytes).
    Removing stm32f10x_rtc.o(.ARM.exidx.text.RTC_GetITStatus), (8 bytes).
    Removing stm32f10x_rtc.o(.text.RTC_ClearITPendingBit), (18 bytes).
    Removing stm32f10x_rtc.o(.ARM.exidx.text.RTC_ClearITPendingBit), (8 bytes).
    Removing stm32f10x_sdio.o(.text), (0 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_DeInit), (38 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_DeInit), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_Init), (52 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_Init), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_StructInit), (16 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_StructInit), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_ClockCmd), (10 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_ClockCmd), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_SetPowerState), (24 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_SetPowerState), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_GetPowerState), (16 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_GetPowerState), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_ITConfig), (24 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_ITConfig), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_DMACmd), (12 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_DMACmd), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_SendCommand), (46 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_SendCommand), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_CmdStructInit), (14 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_CmdStructInit), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_GetCommandResponse), (14 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_GetCommandResponse), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_GetResponse), (26 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_GetResponse), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_DataConfig), (52 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_DataConfig), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_DataStructInit), (20 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_DataStructInit), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_GetDataCounter), (12 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_GetDataCounter), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_ReadData), (12 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_ReadData), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_WriteData), (12 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_WriteData), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_GetFIFOCount), (12 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_GetFIFOCount), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_StartSDIOReadWait), (12 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_StartSDIOReadWait), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_StopSDIOReadWait), (12 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_StopSDIOReadWait), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_SetSDIOReadWaitMode), (12 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_SetSDIOReadWaitMode), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_SetSDIOOperation), (12 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_SetSDIOOperation), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_SendSDIOSuspendCmd), (12 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_SendSDIOSuspendCmd), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_CommandCompletionCmd), (12 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_CommandCompletionCmd), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_CEATAITCmd), (18 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_CEATAITCmd), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_SendCEATACmd), (12 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_SendCEATACmd), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_GetFlagStatus), (18 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_GetFlagStatus), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_ClearFlag), (12 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_ClearFlag), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_GetITStatus), (18 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_GetITStatus), (8 bytes).
    Removing stm32f10x_sdio.o(.text.SDIO_ClearITPendingBit), (12 bytes).
    Removing stm32f10x_sdio.o(.ARM.exidx.text.SDIO_ClearITPendingBit), (8 bytes).
    Removing stm32f10x_spi.o(.text), (0 bytes).
    Removing stm32f10x_spi.o(.text.SPI_I2S_DeInit), (104 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_DeInit), (8 bytes).
    Removing stm32f10x_spi.o(.text.SPI_Init), (70 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.SPI_Init), (8 bytes).
    Removing stm32f10x_spi.o(.text.I2S_Init), (162 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.I2S_Init), (8 bytes).
    Removing stm32f10x_spi.o(.text.SPI_StructInit), (16 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.SPI_StructInit), (8 bytes).
    Removing stm32f10x_spi.o(.text.I2S_StructInit), (14 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.I2S_StructInit), (8 bytes).
    Removing stm32f10x_spi.o(.text.SPI_Cmd), (20 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.SPI_Cmd), (8 bytes).
    Removing stm32f10x_spi.o(.text.I2S_Cmd), (20 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.I2S_Cmd), (8 bytes).
    Removing stm32f10x_spi.o(.text.SPI_I2S_ITConfig), (24 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_ITConfig), (8 bytes).
    Removing stm32f10x_spi.o(.text.SPI_I2S_DMACmd), (16 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_DMACmd), (8 bytes).
    Removing stm32f10x_spi.o(.text.SPI_I2S_SendData), (4 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_SendData), (8 bytes).
    Removing stm32f10x_spi.o(.text.SPI_I2S_ReceiveData), (4 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_ReceiveData), (8 bytes).
    Removing stm32f10x_spi.o(.text.SPI_NSSInternalSoftwareConfig), (24 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.SPI_NSSInternalSoftwareConfig), (8 bytes).
    Removing stm32f10x_spi.o(.text.SPI_SSOutputCmd), (20 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.SPI_SSOutputCmd), (8 bytes).
    Removing stm32f10x_spi.o(.text.SPI_DataSizeConfig), (16 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.SPI_DataSizeConfig), (8 bytes).
    Removing stm32f10x_spi.o(.text.SPI_TransmitCRC), (10 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.SPI_TransmitCRC), (8 bytes).
    Removing stm32f10x_spi.o(.text.SPI_CalculateCRC), (20 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.SPI_CalculateCRC), (8 bytes).
    Removing stm32f10x_spi.o(.text.SPI_GetCRC), (12 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.SPI_GetCRC), (8 bytes).
    Removing stm32f10x_spi.o(.text.SPI_GetCRCPolynomial), (4 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.SPI_GetCRCPolynomial), (8 bytes).
    Removing stm32f10x_spi.o(.text.SPI_BiDirectionalLineConfig), (24 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.SPI_BiDirectionalLineConfig), (8 bytes).
    Removing stm32f10x_spi.o(.text.SPI_I2S_GetFlagStatus), (10 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_GetFlagStatus), (8 bytes).
    Removing stm32f10x_spi.o(.text.SPI_I2S_ClearFlag), (6 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_ClearFlag), (8 bytes).
    Removing stm32f10x_spi.o(.text.SPI_I2S_GetITStatus), (24 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_GetITStatus), (8 bytes).
    Removing stm32f10x_spi.o(.text.SPI_I2S_ClearITPendingBit), (16 bytes).
    Removing stm32f10x_spi.o(.ARM.exidx.text.SPI_I2S_ClearITPendingBit), (8 bytes).
    Removing stm32f10x_tim.o(.text), (0 bytes).
    Removing stm32f10x_tim.o(.text.TIM_DeInit), (560 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_DeInit), (8 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_TimeBaseInit), (8 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_OC1Init), (8 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_OC2Init), (8 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_OC3Init), (8 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_OC4Init), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_ICInit), (608 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_ICInit), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_SetIC1Prescaler), (16 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_SetIC1Prescaler), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_SetIC2Prescaler), (18 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_SetIC2Prescaler), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_SetIC3Prescaler), (16 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_SetIC3Prescaler), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_SetIC4Prescaler), (18 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_SetIC4Prescaler), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_PWMIConfig), (596 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_PWMIConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_BDTRConfig), (40 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_BDTRConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_TimeBaseStructInit), (20 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_TimeBaseStructInit), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_OCStructInit), (12 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_OCStructInit), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_ICStructInit), (12 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_ICStructInit), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_BDTRStructInit), (12 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_BDTRStructInit), (8 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_Cmd), (8 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_CtrlPWMOutputs), (8 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_ITConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_GenerateEvent), (4 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_GenerateEvent), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_DMAConfig), (8 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_DMAConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_DMACmd), (16 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_DMACmd), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_InternalClockConfig), (10 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_InternalClockConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_ITRxExternalClockConfig), (20 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_ITRxExternalClockConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_SelectInputTrigger), (12 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectInputTrigger), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_TIxExternalClockConfig), (280 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_TIxExternalClockConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_ETRClockMode1Config), (30 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_ETRClockMode1Config), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_ETRConfig), (22 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_ETRConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_ETRClockMode2Config), (30 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_ETRClockMode2Config), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_PrescalerConfig), (6 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_PrescalerConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_CounterModeConfig), (12 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_CounterModeConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_EncoderInterfaceConfig), (48 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_EncoderInterfaceConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_ForcedOC1Config), (12 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_ForcedOC1Config), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_ForcedOC2Config), (14 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_ForcedOC2Config), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_ForcedOC3Config), (12 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_ForcedOC3Config), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_ForcedOC4Config), (14 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_ForcedOC4Config), (8 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_ARRPreloadConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_SelectCOM), (20 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectCOM), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_SelectCCDMA), (20 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectCCDMA), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_CCPreloadControl), (18 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_CCPreloadControl), (8 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_OC1PreloadConfig), (8 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_OC2PreloadConfig), (8 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_OC3PreloadConfig), (8 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_OC4PreloadConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_OC1FastConfig), (12 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_OC1FastConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_OC2FastConfig), (14 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_OC2FastConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_OC3FastConfig), (12 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_OC3FastConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_OC4FastConfig), (14 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_OC4FastConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_ClearOC1Ref), (12 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_ClearOC1Ref), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_ClearOC2Ref), (14 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_ClearOC2Ref), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_ClearOC3Ref), (12 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_ClearOC3Ref), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_ClearOC4Ref), (14 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_ClearOC4Ref), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_OC1PolarityConfig), (12 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_OC1PolarityConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_OC1NPolarityConfig), (12 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_OC1NPolarityConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_OC2PolarityConfig), (14 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_OC2PolarityConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_OC2NPolarityConfig), (14 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_OC2NPolarityConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_OC3PolarityConfig), (14 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_OC3PolarityConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_OC3NPolarityConfig), (14 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_OC3NPolarityConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_OC4PolarityConfig), (14 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_OC4PolarityConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_CCxCmd), (32 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_CCxCmd), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_CCxNCmd), (32 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_CCxNCmd), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_SelectOCxM), (84 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectOCxM), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_UpdateDisableConfig), (20 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_UpdateDisableConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_UpdateRequestConfig), (20 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_UpdateRequestConfig), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_SelectHallSensor), (20 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectHallSensor), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_SelectOnePulseMode), (16 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectOnePulseMode), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_SelectOutputTrigger), (16 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectOutputTrigger), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_SelectSlaveMode), (16 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectSlaveMode), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_SelectMasterSlaveMode), (16 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_SelectMasterSlaveMode), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_SetCounter), (4 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_SetCounter), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_SetAutoreload), (4 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_SetAutoreload), (8 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_SetCompare1), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_SetCompare2), (4 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_SetCompare2), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_SetCompare3), (4 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_SetCompare3), (8 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_SetCompare4), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_SetClockDivision), (16 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_SetClockDivision), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_GetCapture1), (4 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_GetCapture1), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_GetCapture2), (4 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_GetCapture2), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_GetCapture3), (4 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_GetCapture3), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_GetCapture4), (6 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_GetCapture4), (8 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_GetCounter), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_GetPrescaler), (4 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_GetPrescaler), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_GetFlagStatus), (10 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_GetFlagStatus), (8 bytes).
    Removing stm32f10x_tim.o(.text.TIM_ClearFlag), (6 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_ClearFlag), (8 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_GetITStatus), (8 bytes).
    Removing stm32f10x_tim.o(.ARM.exidx.text.TIM_ClearITPendingBit), (8 bytes).
    Removing stm32f10x_usart.o(.text), (0 bytes).
    Removing stm32f10x_usart.o(.text.USART_DeInit), (174 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_DeInit), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_Init), (182 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_Init), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_StructInit), (18 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_StructInit), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_ClockInit), (34 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_ClockInit), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_ClockStructInit), (8 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_ClockStructInit), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_Cmd), (20 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_Cmd), (8 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_ITConfig), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_DMACmd), (16 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_DMACmd), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_SetAddress), (16 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_SetAddress), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_WakeUpConfig), (16 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_WakeUpConfig), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_ReceiverWakeUpCmd), (20 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_ReceiverWakeUpCmd), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_LINBreakDetectLengthConfig), (16 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_LINBreakDetectLengthConfig), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_LINCmd), (20 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_LINCmd), (8 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_SendData), (8 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_ReceiveData), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_SendBreak), (10 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_SendBreak), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_SetGuardTime), (16 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_SetGuardTime), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_SetPrescaler), (16 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_SetPrescaler), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_SmartCardCmd), (20 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_SmartCardCmd), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_SmartCardNACKCmd), (20 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_SmartCardNACKCmd), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_HalfDuplexCmd), (20 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_HalfDuplexCmd), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_OverSampling8Cmd), (20 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_OverSampling8Cmd), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_OneBitMethodCmd), (20 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_OneBitMethodCmd), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_IrDAConfig), (16 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_IrDAConfig), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_IrDACmd), (20 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_IrDACmd), (8 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_GetFlagStatus), (8 bytes).
    Removing stm32f10x_usart.o(.text.USART_ClearFlag), (6 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_ClearFlag), (8 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_GetITStatus), (8 bytes).
    Removing stm32f10x_usart.o(.ARM.exidx.text.USART_ClearITPendingBit), (8 bytes).
    Removing stm32f10x_wwdg.o(.text), (0 bytes).
    Removing stm32f10x_wwdg.o(.text.WWDG_DeInit), (26 bytes).
    Removing stm32f10x_wwdg.o(.ARM.exidx.text.WWDG_DeInit), (8 bytes).
    Removing stm32f10x_wwdg.o(.text.WWDG_SetPrescaler), (20 bytes).
    Removing stm32f10x_wwdg.o(.ARM.exidx.text.WWDG_SetPrescaler), (8 bytes).
    Removing stm32f10x_wwdg.o(.text.WWDG_SetWindowValue), (40 bytes).
    Removing stm32f10x_wwdg.o(.ARM.exidx.text.WWDG_SetWindowValue), (8 bytes).
    Removing stm32f10x_wwdg.o(.text.WWDG_EnableIT), (14 bytes).
    Removing stm32f10x_wwdg.o(.ARM.exidx.text.WWDG_EnableIT), (8 bytes).
    Removing stm32f10x_wwdg.o(.text.WWDG_SetCounter), (16 bytes).
    Removing stm32f10x_wwdg.o(.ARM.exidx.text.WWDG_SetCounter), (8 bytes).
    Removing stm32f10x_wwdg.o(.text.WWDG_Enable), (16 bytes).
    Removing stm32f10x_wwdg.o(.ARM.exidx.text.WWDG_Enable), (8 bytes).
    Removing stm32f10x_wwdg.o(.text.WWDG_GetFlagStatus), (14 bytes).
    Removing stm32f10x_wwdg.o(.ARM.exidx.text.WWDG_GetFlagStatus), (8 bytes).
    Removing stm32f10x_wwdg.o(.text.WWDG_ClearFlag), (14 bytes).
    Removing stm32f10x_wwdg.o(.ARM.exidx.text.WWDG_ClearFlag), (8 bytes).
    Removing system_stm32f10x.o(.text), (0 bytes).
    Removing system_stm32f10x.o(.ARM.exidx.text.SystemInit), (8 bytes).
    Removing system_stm32f10x.o(.text.SystemCoreClockUpdate), (94 bytes).
    Removing system_stm32f10x.o(.ARM.exidx.text.SystemCoreClockUpdate), (8 bytes).
    Removing system_stm32f10x.o(.rodata.AHBPrescTable), (16 bytes).
    Removing core_cm3.o(.text), (0 bytes).
    Removing core_cm3.o(.text.__get_PSP), (6 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__get_PSP), (8 bytes).
    Removing core_cm3.o(.text.__set_PSP), (6 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__set_PSP), (8 bytes).
    Removing core_cm3.o(.text.__get_MSP), (6 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__get_MSP), (8 bytes).
    Removing core_cm3.o(.text.__set_MSP), (6 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__set_MSP), (8 bytes).
    Removing core_cm3.o(.text.__get_BASEPRI), (6 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__get_BASEPRI), (8 bytes).
    Removing core_cm3.o(.text.__set_BASEPRI), (6 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__set_BASEPRI), (8 bytes).
    Removing core_cm3.o(.text.__get_PRIMASK), (6 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__get_PRIMASK), (8 bytes).
    Removing core_cm3.o(.text.__set_PRIMASK), (6 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__set_PRIMASK), (8 bytes).
    Removing core_cm3.o(.text.__get_FAULTMASK), (6 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__get_FAULTMASK), (8 bytes).
    Removing core_cm3.o(.text.__set_FAULTMASK), (6 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__set_FAULTMASK), (8 bytes).
    Removing core_cm3.o(.text.__get_CONTROL), (6 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__get_CONTROL), (8 bytes).
    Removing core_cm3.o(.text.__set_CONTROL), (6 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__set_CONTROL), (8 bytes).
    Removing core_cm3.o(.text.__REV), (4 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__REV), (8 bytes).
    Removing core_cm3.o(.text.__REV16), (4 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__REV16), (8 bytes).
    Removing core_cm3.o(.text.__REVSH), (4 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__REVSH), (8 bytes).
    Removing core_cm3.o(.text.__RBIT), (6 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__RBIT), (8 bytes).
    Removing core_cm3.o(.text.__LDREXB), (8 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__LDREXB), (8 bytes).
    Removing core_cm3.o(.text.__LDREXH), (8 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__LDREXH), (8 bytes).
    Removing core_cm3.o(.text.__LDREXW), (6 bytes).
    Removing core_cm3.o(.ARM.exidx.text.__LDREXW), (8 bytes).
    Removing main.o(.text), (0 bytes).
    Removing main.o(.ARM.exidx.text.main), (8 bytes).
    Removing main.o(.ARM.use_no_argv), (4 bytes).
    Removing stm32f10x_it.o(.text), (0 bytes).
    Removing stm32f10x_it.o(.ARM.exidx.text.NMI_Handler), (8 bytes).
    Removing stm32f10x_it.o(.ARM.exidx.text.HardFault_Handler), (8 bytes).
    Removing stm32f10x_it.o(.ARM.exidx.text.MemManage_Handler), (8 bytes).
    Removing stm32f10x_it.o(.ARM.exidx.text.BusFault_Handler), (8 bytes).
    Removing stm32f10x_it.o(.ARM.exidx.text.UsageFault_Handler), (8 bytes).
    Removing stm32f10x_it.o(.ARM.exidx.text.SVC_Handler), (8 bytes).
    Removing stm32f10x_it.o(.ARM.exidx.text.DebugMon_Handler), (8 bytes).
    Removing stm32f10x_it.o(.ARM.exidx.text.PendSV_Handler), (8 bytes).
    Removing stm32f10x_it.o(.ARM.exidx.text.SysTick_Handler), (8 bytes).
    Removing gpio_driver.o(.text), (0 bytes).
    Removing gpio_driver.o(.text.GPIO_InitPin), (88 bytes).
    Removing gpio_driver.o(.ARM.exidx.text.GPIO_InitPin), (8 bytes).
    Removing gpio_driver.o(.ARM.exidx.text.GPIO_SetPin), (8 bytes).
    Removing gpio_driver.o(.text.GPIO_GetPin), (4 bytes).
    Removing gpio_driver.o(.ARM.exidx.text.GPIO_GetPin), (8 bytes).
    Removing gpio_driver.o(.text.GPIO_TogglePin), (36 bytes).
    Removing gpio_driver.o(.ARM.exidx.text.GPIO_TogglePin), (8 bytes).
    Removing pwm.o(.text), (0 bytes).
    Removing pwm.o(.ARM.exidx.text.PWM_Init), (8 bytes).
    Removing pwm.o(.ARM.exidx.text.PWM_SetDutyCycle), (8 bytes).
    Removing pwm.o(.ARM.exidx.text.PWM_Start), (8 bytes).
    Removing pwm.o(.text.PWM_Stop), (30 bytes).
    Removing pwm.o(.ARM.exidx.text.PWM_Stop), (8 bytes).
    Removing usart.o(.text), (0 bytes).
    Removing usart.o(.text.USART1_Init), (132 bytes).
    Removing usart.o(.ARM.exidx.text.USART1_Init), (8 bytes).
    Removing usart.o(.text.USART1_SendChar), (36 bytes).
    Removing usart.o(.ARM.exidx.text.USART1_SendChar), (8 bytes).
    Removing usart.o(.ARM.exidx.text.USART1_SendString), (8 bytes).
    Removing usart.o(.text.USART1_Printf), (82 bytes).
    Removing usart.o(.ARM.exidx.text.USART1_Printf), (8 bytes).
    Removing usart.o(.ARM.exidx.text.USART1_RxCpltCallback), (8 bytes).
    Removing usart.o(.ARM.exidx.text.USART1_IRQHandler), (8 bytes).
    Removing usart.o(.text.USART1_ReceiveChar), (34 bytes).
    Removing usart.o(.ARM.exidx.text.USART1_ReceiveChar), (8 bytes).
    Removing usart.o(.text.USART1_ReceiveString), (108 bytes).
    Removing usart.o(.ARM.exidx.text.USART1_ReceiveString), (8 bytes).
    Removing timer.o(.text), (0 bytes).
    Removing timer.o(.ARM.exidx.text.TIM3_Config), (8 bytes).
    Removing timer.o(.ARM.exidx.text.TIM3_IRQHandler), (8 bytes).
    Removing encoder.o(.text), (0 bytes).
    Removing encoder.o(.ARM.exidx.text.EXTI0_IRQHandler), (8 bytes).
    Removing encoder.o(.ARM.exidx.text.EXTI2_IRQHandler), (8 bytes).
    Removing encoder.o(.ARM.exidx.text.TIM2_IRQHandler), (8 bytes).
    Removing encoder.o(.ARM.exidx.text.Encoder_Init), (8 bytes).
    Removing encoder.o(.ARM.exidx.text.Encoder_GetData), (8 bytes).
    Removing encoder.o(.ARM.exidx.text.Show_Encoder), (8 bytes).
    Removing pid.o(.text), (0 bytes).
    Removing pid.o(.ARM.exidx.text.PID_Init), (8 bytes).
    Removing pid.o(.ARM.exidx.text.PID_SetTarget), (8 bytes).
    Removing pid.o(.ARM.exidx.text.PID_Update), (8 bytes).
    Removing oled.o(.text), (0 bytes).
    Removing oled.o(.text.OLED_W_SCL), (18 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_W_SCL), (8 bytes).
    Removing oled.o(.text.OLED_W_SDA), (18 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_W_SDA), (8 bytes).
    Removing oled.o(.text.OLED_GPIO_Init), (84 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_GPIO_Init), (8 bytes).
    Removing oled.o(.text.OLED_I2C_Start), (62 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_I2C_Start), (8 bytes).
    Removing oled.o(.text.OLED_I2C_Stop), (50 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_I2C_Stop), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_I2C_SendByte), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_WriteCommand), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_WriteData), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Init), (8 bytes).
    Removing oled.o(.text.OLED_Clear), (20 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Clear), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Update), (8 bytes).
    Removing oled.o(.text.OLED_SetCursor), (32 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_SetCursor), (8 bytes).
    Removing oled.o(.text.OLED_Pow), (22 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Pow), (8 bytes).
    Removing oled.o(.text.OLED_pnpoly), (128 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_pnpoly), (8 bytes).
    Removing oled.o(.text.OLED_IsInAngle), (124 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_IsInAngle), (8 bytes).
    Removing oled.o(.text.OLED_UpdateArea), (132 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_UpdateArea), (8 bytes).
    Removing oled.o(.text.OLED_ClearArea), (110 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ClearArea), (8 bytes).
    Removing oled.o(.text.OLED_Reverse), (48 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Reverse), (8 bytes).
    Removing oled.o(.text.OLED_ReverseArea), (106 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ReverseArea), (8 bytes).
    Removing oled.o(.text.OLED_ShowChar), (70 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowChar), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowImage), (8 bytes).
    Removing oled.o(.text.OLED_ShowString), (320 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowString), (8 bytes).
    Removing oled.o(.text.OLED_ShowNum), (188 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowNum), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowSignedNum), (8 bytes).
    Removing oled.o(.text.OLED_ShowHexNum), (222 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowHexNum), (8 bytes).
    Removing oled.o(.text.OLED_ShowBinNum), (160 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowBinNum), (8 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_ShowFloatNum), (8 bytes).
    Removing oled.o(.text.OLED_Printf), (40 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_Printf), (8 bytes).
    Removing oled.o(.text.OLED_DrawPoint), (48 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_DrawPoint), (8 bytes).
    Removing oled.o(.text.OLED_GetPoint), (46 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_GetPoint), (8 bytes).
    Removing oled.o(.text.OLED_DrawLine), (708 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_DrawLine), (8 bytes).
    Removing oled.o(.text.OLED_DrawRectangle), (336 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_DrawRectangle), (8 bytes).
    Removing oled.o(.text.OLED_DrawTriangle), (360 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_DrawTriangle), (8 bytes).
    Removing oled.o(.text.OLED_DrawCircle), (1192 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_DrawCircle), (8 bytes).
    Removing oled.o(.text.OLED_DrawEllipse), (1670 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_DrawEllipse), (8 bytes).
    Removing oled.o(.text.OLED_DrawArc), (2532 bytes).
    Removing oled.o(.ARM.exidx.text.OLED_DrawArc), (8 bytes).
    Removing oled_data.o(.text), (0 bytes).
    Removing oled_data.o(.rodata.OLED_CF16x16), (245 bytes).
    Removing oled_data.o(.rodata.Diode), (32 bytes).
    Removing motor_control.o(.text), (0 bytes).
    Removing motor_control.o(.ARM.exidx.text.MotorControl_Init), (8 bytes).
    Removing motor_control.o(.ARM.exidx.text.MotorControl_SetSpeed), (8 bytes).
    Removing motor_control.o(.ARM.exidx.text.MotorControl_Update), (8 bytes).

1097 unused section(s) (total 32085 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_ctype_table.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command_hlt.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit_hlt.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch_hlt.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/btod_accurate.c                  0x00000000   Number         0  btod_accurate.o ABSOLUTE
    ../clib/btod_accurate.c                  0x00000000   Number         0  btod_accurate_common.o ABSOLUTE
    ../clib/btod_accurate.c                  0x00000000   Number         0  dtob_accurate.o ABSOLUTE
    ../clib/ctype.c                          0x00000000   Number         0  isspace.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/locale.c                         0x00000000   Number         0  _c16rtomb.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_ctype_c.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  vsprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  vsnprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2snprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2snprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_pad.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_truncate.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_charcount.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec_accurate.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _snputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wctomb.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_longlong_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_hex.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wchar.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_c.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_n.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_p.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_o.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_i.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_u.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_e.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_g.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_a.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lli.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lld.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llu.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ll.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_l.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lc.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ls.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llo.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llx.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  strtol.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _strtoul.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  strtof.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _chval.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  strtod.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _sgetc.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_fp.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_fp_accurate.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_hexfp.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_infnan.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  memcmp.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strlen.o ABSOLUTE
    ../fplib/d2f.s                           0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/daddsub.s                       0x00000000   Number         0  daddsub_clz.o ABSOLUTE
    ../fplib/dcheck1.s                       0x00000000   Number         0  dcheck1.o ABSOLUTE
    ../fplib/dcmp.s                          0x00000000   Number         0  dcmp.o ABSOLUTE
    ../fplib/dcmpi.s                         0x00000000   Number         0  dcmpi.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/deqf.s                          0x00000000   Number         0  deqf.o ABSOLUTE
    ../fplib/dfix.s                          0x00000000   Number         0  dfix.o ABSOLUTE
    ../fplib/dfixu.s                         0x00000000   Number         0  dfixu.o ABSOLUTE
    ../fplib/dflt.s                          0x00000000   Number         0  dflt_clz.o ABSOLUTE
    ../fplib/dgeqf.s                         0x00000000   Number         0  dgeqf.o ABSOLUTE
    ../fplib/dleqf.s                         0x00000000   Number         0  dleqf.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/drleqf.s                        0x00000000   Number         0  drleqf.o ABSOLUTE
    ../fplib/drnd.s                          0x00000000   Number         0  drnd.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/faddsub.s                       0x00000000   Number         0  faddsub_clz.o ABSOLUTE
    ../fplib/fcmp.s                          0x00000000   Number         0  fcmp.o ABSOLUTE
    ../fplib/fcmpi.s                         0x00000000   Number         0  fcmpi.o ABSOLUTE
    ../fplib/fdiv.s                          0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/feqf.s                          0x00000000   Number         0  feqf.o ABSOLUTE
    ../fplib/ffix.s                          0x00000000   Number         0  ffix.o ABSOLUTE
    ../fplib/fflt.s                          0x00000000   Number         0  fflt_clz.o ABSOLUTE
    ../fplib/fgeqf.s                         0x00000000   Number         0  fgeqf.o ABSOLUTE
    ../fplib/fleqf.s                         0x00000000   Number         0  fleqf.o ABSOLUTE
    ../fplib/fmul.s                          0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpconst.s                       0x00000000   Number         0  fpconst.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fpinit_empty.s                  0x00000000   Number         0  fpinit_empty.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/printf2.s                       0x00000000   Number         0  printf2.o ABSOLUTE
    ../fplib/printf2a.s                      0x00000000   Number         0  printf2a.o ABSOLUTE
    ../fplib/printf2b.s                      0x00000000   Number         0  printf2b.o ABSOLUTE
    ../fplib/retnan.s                        0x00000000   Number         0  retnan.o ABSOLUTE
    ../fplib/scalbn.s                        0x00000000   Number         0  scalbn.o ABSOLUTE
    ../fplib/scanf1.s                        0x00000000   Number         0  scanf1.o ABSOLUTE
    ../fplib/scanf2.s                        0x00000000   Number         0  scanf2.o ABSOLUTE
    ../fplib/scanf2a.s                       0x00000000   Number         0  scanf2a.o ABSOLUTE
    ../fplib/scanf2b.s                       0x00000000   Number         0  scanf2b.o ABSOLUTE
    ../fplib/trapv.s                         0x00000000   Number         0  trapv.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/frexp.c                       0x00000000   Number         0  frexp.o ABSOLUTE
    ../mathlib/ldexp.c                       0x00000000   Number         0  ldexp.o ABSOLUTE
    ../mathlib/narrow.c                      0x00000000   Number         0  narrow.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/round.c                       0x00000000   Number         0  round.o ABSOLUTE
    ../mathlib/strtof.c                      0x00000000   Number         0  strtof.o ABSOLUTE
    OLED.c                                   0x00000000   Number         0  oled.o ABSOLUTE
    OLED_Data.c                              0x00000000   Number         0  oled_data.o ABSOLUTE
    Start/startup_stm32f10x_md.s             0x00000000   Number         0  startup_stm32f10x_md.o ABSOLUTE
    core_cm3.c                               0x00000000   Number         0  core_cm3.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    encoder.c                                0x00000000   Number         0  encoder.o ABSOLUTE
    gpio_driver.c                            0x00000000   Number         0  gpio_driver.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    misc.c                                   0x00000000   Number         0  misc.o ABSOLUTE
    motor_control.c                          0x00000000   Number         0  motor_control.o ABSOLUTE
    pid.c                                    0x00000000   Number         0  pid.o ABSOLUTE
    pwm.c                                    0x00000000   Number         0  pwm.o ABSOLUTE
    stm32f10x_adc.c                          0x00000000   Number         0  stm32f10x_adc.o ABSOLUTE
    stm32f10x_bkp.c                          0x00000000   Number         0  stm32f10x_bkp.o ABSOLUTE
    stm32f10x_can.c                          0x00000000   Number         0  stm32f10x_can.o ABSOLUTE
    stm32f10x_cec.c                          0x00000000   Number         0  stm32f10x_cec.o ABSOLUTE
    stm32f10x_crc.c                          0x00000000   Number         0  stm32f10x_crc.o ABSOLUTE
    stm32f10x_dac.c                          0x00000000   Number         0  stm32f10x_dac.o ABSOLUTE
    stm32f10x_dbgmcu.c                       0x00000000   Number         0  stm32f10x_dbgmcu.o ABSOLUTE
    stm32f10x_dma.c                          0x00000000   Number         0  stm32f10x_dma.o ABSOLUTE
    stm32f10x_exti.c                         0x00000000   Number         0  stm32f10x_exti.o ABSOLUTE
    stm32f10x_flash.c                        0x00000000   Number         0  stm32f10x_flash.o ABSOLUTE
    stm32f10x_fsmc.c                         0x00000000   Number         0  stm32f10x_fsmc.o ABSOLUTE
    stm32f10x_gpio.c                         0x00000000   Number         0  stm32f10x_gpio.o ABSOLUTE
    stm32f10x_i2c.c                          0x00000000   Number         0  stm32f10x_i2c.o ABSOLUTE
    stm32f10x_it.c                           0x00000000   Number         0  stm32f10x_it.o ABSOLUTE
    stm32f10x_iwdg.c                         0x00000000   Number         0  stm32f10x_iwdg.o ABSOLUTE
    stm32f10x_pwr.c                          0x00000000   Number         0  stm32f10x_pwr.o ABSOLUTE
    stm32f10x_rcc.c                          0x00000000   Number         0  stm32f10x_rcc.o ABSOLUTE
    stm32f10x_rtc.c                          0x00000000   Number         0  stm32f10x_rtc.o ABSOLUTE
    stm32f10x_sdio.c                         0x00000000   Number         0  stm32f10x_sdio.o ABSOLUTE
    stm32f10x_spi.c                          0x00000000   Number         0  stm32f10x_spi.o ABSOLUTE
    stm32f10x_tim.c                          0x00000000   Number         0  stm32f10x_tim.o ABSOLUTE
    stm32f10x_usart.c                        0x00000000   Number         0  stm32f10x_usart.o ABSOLUTE
    stm32f10x_wwdg.c                         0x00000000   Number         0  stm32f10x_wwdg.o ABSOLUTE
    system_stm32f10x.c                       0x00000000   Number         0  system_stm32f10x.o ABSOLUTE
    timer.c                                  0x00000000   Number         0  timer.o ABSOLUTE
    usart.c                                  0x00000000   Number         0  usart.o ABSOLUTE
    RESET                                    0x08000000   Section      236  startup_stm32f10x_md.o(RESET)
    !!!main                                  0x080000ec   Section        8  __main.o(!!!main)
    !!!scatter                               0x080000f4   Section       92  __scatter.o(!!!scatter)
    !!handler_copy                           0x08000150   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_null                           0x0800016c   Section        2  __scatter.o(!!handler_null)
    !!handler_zi                             0x08000170   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x0800018c   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000003  0x0800018c   Section        6  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    .ARM.Collect$$_printf_percent$$00000009  0x08000192   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$00000017  0x08000198   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x0800019c   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x0800019e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x0800019e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$00000006          0x0800019e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000006)
    .ARM.Collect$$libinit$$0000000C          0x0800019e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x0800019e   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$00000010          0x0800019e   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000010)
    .ARM.Collect$$libinit$$00000011          0x0800019e   Section        6  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000013          0x080001a4   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000014          0x080001a4   Section       12  libinit2.o(.ARM.Collect$$libinit$$00000014)
    .ARM.Collect$$libinit$$00000015          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000017          0x080001b0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000018          0x080001b0   Section       10  libinit2.o(.ARM.Collect$$libinit$$00000018)
    .ARM.Collect$$libinit$$00000019          0x080001ba   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x080001ba   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x080001ba   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x080001ba   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x080001ba   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x080001ba   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x080001ba   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$00000027          0x080001ba   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000027)
    .ARM.Collect$$libinit$$0000002E          0x080001ba   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x080001ba   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x080001ba   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000034          0x080001ba   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000034)
    .ARM.Collect$$libinit$$00000035          0x080001ba   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000035)
    .ARM.Collect$$libshutdown$$00000000      0x080001bc   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x080001be   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x080001be   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x080001be   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x080001be   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x080001be   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x080001be   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x080001be   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x080001c0   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x080001c0   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x080001c0   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x080001c6   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x080001c6   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x080001ca   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x080001ca   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x080001d2   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x080001d4   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x080001d4   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x080001d8   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x080001e0   Section       64  startup_stm32f10x_md.o(.text)
    .text                                    0x08000220   Section        0  __2snprintf.o(.text)
    .text                                    0x08000260   Section        0  _printf_dec.o(.text)
    .text                                    0x080002d8   Section        0  __printf_flags_ss_wp.o(.text)
    .text                                    0x08000460   Section        0  strtol.o(.text)
    .text                                    0x080004d0   Section        0  memcmp.o(.text)
    .text                                    0x08000528   Section       78  rt_memclr_w.o(.text)
    .text                                    0x08000578   Section      128  strcmpv7m.o(.text)
    .text                                    0x080005f8   Section        0  heapauxi.o(.text)
    .text                                    0x08000600   Section       16  rt_ctype_table.o(.text)
    .text                                    0x08000610   Section        8  rt_errno_addr_intlibspace.o(.text)
    .text                                    0x08000618   Section        0  _rserrno.o(.text)
    .text                                    0x0800062e   Section        0  _printf_intcommon.o(.text)
    _fp_digits                               0x080006e1   Thumb Code   432  _printf_fp_dec.o(.text)
    .text                                    0x080006e0   Section        0  _printf_fp_dec.o(.text)
    _printf_input_char                       0x08000afd   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x08000afc   Section        0  _printf_char_common.o(.text)
    .text                                    0x08000b2c   Section        0  _snputc.o(.text)
    .text                                    0x08000b3c   Section        0  _strtoul.o(.text)
    .text                                    0x08000bda   Section        0  strtof.o(.text)
    .text                                    0x08000be4   Section        8  libspace.o(.text)
    .text                                    0x08000bec   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x08000bf4   Section      138  lludiv10.o(.text)
    .text                                    0x08000c80   Section        0  _printf_fp_infnan.o(.text)
    .text                                    0x08000d00   Section        0  _chval.o(.text)
    _local_sscanf                            0x08000d1d   Thumb Code    68  strtod.o(.text)
    .text                                    0x08000d1c   Section        0  strtod.o(.text)
    .text                                    0x08000dcc   Section        0  bigflt0.o(.text)
    .text                                    0x08000eb0   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08000efa   Section        0  isspace.o(.text)
    .text                                    0x08000f0c   Section        0  _sgetc.o(.text)
    .text                                    0x08000f4c   Section        0  exit.o(.text)
    _fp_value                                0x08000f61   Thumb Code   588  scanf_fp.o(.text)
    .text                                    0x08000f60   Section        0  scanf_fp.o(.text)
    .text                                    0x08001458   Section       38  llshl.o(.text)
    .text                                    0x08001480   Section        0  sys_exit.o(.text)
    .text                                    0x0800148c   Section        0  scanf_hexfp.o(.text)
    .text                                    0x080017ac   Section        0  scanf_infnan.o(.text)
    .text                                    0x080018e0   Section        2  use_no_semi.o(.text)
    .text                                    0x080018e2   Section        0  indicate_semi.o(.text)
    [Anonymous Symbol]                       0x080018e4   Section        0  stm32f10x_it.o(.text.BusFault_Handler)
    [Anonymous Symbol]                       0x080018e8   Section        0  stm32f10x_it.o(.text.DebugMon_Handler)
    [Anonymous Symbol]                       0x080018ec   Section        0  encoder.o(.text.EXTI0_IRQHandler)
    [Anonymous Symbol]                       0x08001940   Section        0  encoder.o(.text.EXTI2_IRQHandler)
    [Anonymous Symbol]                       0x080019a8   Section        0  stm32f10x_exti.o(.text.EXTI_ClearITPendingBit)
    [Anonymous Symbol]                       0x080019b4   Section        0  stm32f10x_exti.o(.text.EXTI_GetITStatus)
    [Anonymous Symbol]                       0x080019d4   Section        0  stm32f10x_exti.o(.text.EXTI_Init)
    [Anonymous Symbol]                       0x08001a3c   Section        0  encoder.o(.text.Encoder_GetData)
    [Anonymous Symbol]                       0x08001a74   Section        0  encoder.o(.text.Encoder_Init)
    [Anonymous Symbol]                       0x08001b54   Section        0  stm32f10x_gpio.o(.text.GPIO_EXTILineConfig)
    [Anonymous Symbol]                       0x08001b8c   Section        0  stm32f10x_gpio.o(.text.GPIO_Init)
    [Anonymous Symbol]                       0x08001c48   Section        0  stm32f10x_gpio.o(.text.GPIO_ReadInputDataBit)
    [Anonymous Symbol]                       0x08001c54   Section        0  gpio_driver.o(.text.GPIO_SetPin)
    [Anonymous Symbol]                       0x08001c58   Section        0  stm32f10x_gpio.o(.text.GPIO_WriteBit)
    [Anonymous Symbol]                       0x08001c64   Section        0  stm32f10x_it.o(.text.HardFault_Handler)
    [Anonymous Symbol]                       0x08001c68   Section        0  stm32f10x_it.o(.text.MemManage_Handler)
    [Anonymous Symbol]                       0x08001c6c   Section        0  motor_control.o(.text.MotorControl_Init)
    [Anonymous Symbol]                       0x08001c98   Section        0  motor_control.o(.text.MotorControl_SetSpeed)
    [Anonymous Symbol]                       0x08001ca0   Section        0  motor_control.o(.text.MotorControl_Update)
    [Anonymous Symbol]                       0x08001cc8   Section        0  stm32f10x_it.o(.text.NMI_Handler)
    [Anonymous Symbol]                       0x08001ccc   Section        0  misc.o(.text.NVIC_Init)
    [Anonymous Symbol]                       0x08001d2c   Section        0  oled.o(.text.OLED_I2C_SendByte)
    [Anonymous Symbol]                       0x08001d88   Section        0  oled.o(.text.OLED_Init)
    [Anonymous Symbol]                       0x08001ea4   Section        0  oled.o(.text.OLED_ShowFloatNum)
    [Anonymous Symbol]                       0x08002148   Section        0  oled.o(.text.OLED_ShowImage)
    [Anonymous Symbol]                       0x08002274   Section        0  oled.o(.text.OLED_ShowSignedNum)
    [Anonymous Symbol]                       0x080023ac   Section        0  oled.o(.text.OLED_Update)
    [Anonymous Symbol]                       0x080023e4   Section        0  oled.o(.text.OLED_WriteCommand)
    [Anonymous Symbol]                       0x08002498   Section        0  oled.o(.text.OLED_WriteData)
    [Anonymous Symbol]                       0x0800251c   Section        0  pid.o(.text.PID_Init)
    [Anonymous Symbol]                       0x08002534   Section        0  pid.o(.text.PID_SetTarget)
    [Anonymous Symbol]                       0x08002540   Section        0  pid.o(.text.PID_Update)
    [Anonymous Symbol]                       0x080025bc   Section        0  pwm.o(.text.PWM_Init)
    [Anonymous Symbol]                       0x080026d0   Section        0  pwm.o(.text.PWM_SetDutyCycle)
    [Anonymous Symbol]                       0x08002728   Section        0  pwm.o(.text.PWM_Start)
    [Anonymous Symbol]                       0x08002748   Section        0  stm32f10x_it.o(.text.PendSV_Handler)
    [Anonymous Symbol]                       0x0800274c   Section        0  stm32f10x_rcc.o(.text.RCC_APB1PeriphClockCmd)
    [Anonymous Symbol]                       0x08002764   Section        0  stm32f10x_rcc.o(.text.RCC_APB2PeriphClockCmd)
    [Anonymous Symbol]                       0x0800277c   Section        0  stm32f10x_it.o(.text.SVC_Handler)
    [Anonymous Symbol]                       0x08002780   Section        0  encoder.o(.text.Show_Encoder)
    [Anonymous Symbol]                       0x08002804   Section        0  stm32f10x_it.o(.text.SysTick_Handler)
    [Anonymous Symbol]                       0x08002808   Section        0  system_stm32f10x.o(.text.SystemInit)
    [Anonymous Symbol]                       0x08002918   Section        0  encoder.o(.text.TIM2_IRQHandler)
    [Anonymous Symbol]                       0x08002968   Section        0  timer.o(.text.TIM3_Config)
    [Anonymous Symbol]                       0x080029dc   Section        0  timer.o(.text.TIM3_IRQHandler)
    [Anonymous Symbol]                       0x08002a0c   Section        0  stm32f10x_tim.o(.text.TIM_ARRPreloadConfig)
    [Anonymous Symbol]                       0x08002a20   Section        0  stm32f10x_tim.o(.text.TIM_ClearITPendingBit)
    [Anonymous Symbol]                       0x08002a28   Section        0  stm32f10x_tim.o(.text.TIM_Cmd)
    [Anonymous Symbol]                       0x08002a3c   Section        0  stm32f10x_tim.o(.text.TIM_CtrlPWMOutputs)
    [Anonymous Symbol]                       0x08002a54   Section        0  stm32f10x_tim.o(.text.TIM_GetCounter)
    [Anonymous Symbol]                       0x08002a58   Section        0  stm32f10x_tim.o(.text.TIM_GetITStatus)
    [Anonymous Symbol]                       0x08002a70   Section        0  stm32f10x_tim.o(.text.TIM_ITConfig)
    [Anonymous Symbol]                       0x08002a80   Section        0  stm32f10x_tim.o(.text.TIM_OC1Init)
    [Anonymous Symbol]                       0x08002af0   Section        0  stm32f10x_tim.o(.text.TIM_OC1PreloadConfig)
    [Anonymous Symbol]                       0x08002afc   Section        0  stm32f10x_tim.o(.text.TIM_OC2Init)
    [Anonymous Symbol]                       0x08002b70   Section        0  stm32f10x_tim.o(.text.TIM_OC2PreloadConfig)
    [Anonymous Symbol]                       0x08002b80   Section        0  stm32f10x_tim.o(.text.TIM_OC3Init)
    [Anonymous Symbol]                       0x08002bf4   Section        0  stm32f10x_tim.o(.text.TIM_OC3PreloadConfig)
    [Anonymous Symbol]                       0x08002c00   Section        0  stm32f10x_tim.o(.text.TIM_OC4Init)
    [Anonymous Symbol]                       0x08002c5c   Section        0  stm32f10x_tim.o(.text.TIM_OC4PreloadConfig)
    [Anonymous Symbol]                       0x08002c6c   Section        0  stm32f10x_tim.o(.text.TIM_SetCompare1)
    [Anonymous Symbol]                       0x08002c70   Section        0  stm32f10x_tim.o(.text.TIM_SetCompare4)
    [Anonymous Symbol]                       0x08002c78   Section        0  stm32f10x_tim.o(.text.TIM_TimeBaseInit)
    [Anonymous Symbol]                       0x08002d24   Section        0  usart.o(.text.USART1_IRQHandler)
    [Anonymous Symbol]                       0x08002d78   Section        0  usart.o(.text.USART1_RxCpltCallback)
    [Anonymous Symbol]                       0x08003220   Section        0  usart.o(.text.USART1_SendString)
    [Anonymous Symbol]                       0x08003254   Section        0  stm32f10x_usart.o(.text.USART_ClearITPendingBit)
    [Anonymous Symbol]                       0x08003264   Section        0  stm32f10x_usart.o(.text.USART_GetFlagStatus)
    [Anonymous Symbol]                       0x08003270   Section        0  stm32f10x_usart.o(.text.USART_GetITStatus)
    [Anonymous Symbol]                       0x0800329c   Section        0  stm32f10x_usart.o(.text.USART_ITConfig)
    [Anonymous Symbol]                       0x080032cc   Section        0  stm32f10x_usart.o(.text.USART_ReceiveData)
    [Anonymous Symbol]                       0x080032d4   Section        0  stm32f10x_usart.o(.text.USART_SendData)
    [Anonymous Symbol]                       0x080032dc   Section        0  stm32f10x_it.o(.text.UsageFault_Handler)
    [Anonymous Symbol]                       0x080032e0   Section        0  main.o(.text.main)
    CL$$btod_d2e                             0x08003396   Section       62  btod.o(CL$$btod_d2e)
    CL$$btod_d2e_denorm_low                  0x080033d4   Section       70  btod.o(CL$$btod_d2e_denorm_low)
    CL$$btod_d2e_norm_op1                    0x0800341a   Section       96  btod.o(CL$$btod_d2e_norm_op1)
    CL$$btod_div_common                      0x0800347c   Section      824  btod.o(CL$$btod_div_common)
    CL$$btod_e2d                             0x080037b4   Section      132  btod.o(CL$$btod_e2d)
    CL$$btod_e2e                             0x08003838   Section      220  btod.o(CL$$btod_e2e)
    CL$$btod_ediv                            0x08003914   Section       42  btod.o(CL$$btod_ediv)
    CL$$btod_edivd                           0x0800393e   Section       42  btod.o(CL$$btod_edivd)
    CL$$btod_emul                            0x08003968   Section       42  btod.o(CL$$btod_emul)
    CL$$btod_emuld                           0x08003992   Section       42  btod.o(CL$$btod_emuld)
    CL$$btod_mult_common                     0x080039bc   Section      580  btod.o(CL$$btod_mult_common)
    i.__ARM_fpclassify                       0x08003c00   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__mathlib_dbl_overflow                 0x08003c28   Section        0  dunder.o(i.__mathlib_dbl_overflow)
    i.__mathlib_dbl_underflow                0x08003c38   Section        0  dunder.o(i.__mathlib_dbl_underflow)
    i.__mathlib_narrow                       0x08003c48   Section        0  narrow.o(i.__mathlib_narrow)
    i.__mathlib_tofloat                      0x08003c58   Section        0  narrow.o(i.__mathlib_tofloat)
    i.__support_ldexp                        0x08003d14   Section        0  ldexp.o(i.__support_ldexp)
    i._is_digit                              0x08003d2a   Section        0  __printf_wp.o(i._is_digit)
    i.frexp                                  0x08003d38   Section        0  frexp.o(i.frexp)
    i.ldexp                                  0x08003d90   Section        0  ldexp.o(i.ldexp)
    i.round                                  0x08003e04   Section        0  round.o(i.round)
    i.strtof                                 0x08003e98   Section        0  strtof.o(i.strtof)
    locale$$code                             0x08003eac   Section       44  lc_ctype_c.o(locale$$code)
    locale$$code                             0x08003ed8   Section       44  lc_numeric_c.o(locale$$code)
    x$fpl$d2f                                0x08003f04   Section       98  d2f.o(x$fpl$d2f)
    x$fpl$dadd                               0x08003f68   Section      336  daddsub_clz.o(x$fpl$dadd)
    _dadd1                                   0x08003f79   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    x$fpl$dcheck1                            0x080040b8   Section       16  dcheck1.o(x$fpl$dcheck1)
    x$fpl$dcmpinf                            0x080040c8   Section       24  dcmpi.o(x$fpl$dcmpinf)
    x$fpl$deqf                               0x080040e0   Section      120  deqf.o(x$fpl$deqf)
    x$fpl$dfixu                              0x08004158   Section       90  dfixu.o(x$fpl$dfixu)
    x$fpl$dfltu                              0x080041b2   Section       38  dflt_clz.o(x$fpl$dfltu)
    x$fpl$dgeqf                              0x080041d8   Section      120  dgeqf.o(x$fpl$dgeqf)
    x$fpl$dleqf                              0x08004250   Section      120  dleqf.o(x$fpl$dleqf)
    x$fpl$dmul                               0x080042c8   Section      340  dmul.o(x$fpl$dmul)
    x$fpl$dnaninf                            0x0800441c   Section      156  dnaninf.o(x$fpl$dnaninf)
    x$fpl$dretinf                            0x080044b8   Section       12  dretinf.o(x$fpl$dretinf)
    x$fpl$drleqf                             0x080044c4   Section      108  drleqf.o(x$fpl$drleqf)
    x$fpl$drnd                               0x08004530   Section      180  drnd.o(x$fpl$drnd)
    x$fpl$drsb                               0x080045e4   Section       22  daddsub_clz.o(x$fpl$drsb)
    x$fpl$dsub                               0x080045fc   Section      476  daddsub_clz.o(x$fpl$dsub)
    _dsub1                                   0x0800460d   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    x$fpl$f2d                                0x080047d8   Section       86  f2d.o(x$fpl$f2d)
    x$fpl$fadd                               0x08004830   Section      196  faddsub_clz.o(x$fpl$fadd)
    _fadd1                                   0x0800483f   Thumb Code     0  faddsub_clz.o(x$fpl$fadd)
    x$fpl$fcmp                               0x080048f4   Section       84  dcmp.o(x$fpl$fcmp)
    x$fpl$fcmp                               0x08004948   Section       84  fcmp.o(x$fpl$fcmp)
    x$fpl$fcmpinf                            0x0800499c   Section       24  fcmpi.o(x$fpl$fcmpinf)
    _fdiv1                                   0x080049b5   Thumb Code     0  fdiv.o(x$fpl$fdiv)
    x$fpl$fdiv                               0x080049b4   Section      388  fdiv.o(x$fpl$fdiv)
    x$fpl$feqf                               0x08004b38   Section      104  feqf.o(x$fpl$feqf)
    x$fpl$ffix                               0x08004ba0   Section       54  ffix.o(x$fpl$ffix)
    x$fpl$fflt                               0x08004bd8   Section       48  fflt_clz.o(x$fpl$fflt)
    x$fpl$ffltu                              0x08004c08   Section       38  fflt_clz.o(x$fpl$ffltu)
    x$fpl$fgeqf                              0x08004c30   Section      104  fgeqf.o(x$fpl$fgeqf)
    x$fpl$fleqf                              0x08004c98   Section      104  fleqf.o(x$fpl$fleqf)
    x$fpl$fmul                               0x08004d00   Section      258  fmul.o(x$fpl$fmul)
    x$fpl$fnaninf                            0x08004e02   Section      140  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fretinf                            0x08004e8e   Section       10  fretinf.o(x$fpl$fretinf)
    x$fpl$fsub                               0x08004e98   Section      234  faddsub_clz.o(x$fpl$fsub)
    _fsub1                                   0x08004ea7   Thumb Code     0  faddsub_clz.o(x$fpl$fsub)
    x$fpl$ieeestatus                         0x08004f82   Section        6  istatus.o(x$fpl$ieeestatus)
    x$fpl$printf1                            0x08004f88   Section        4  printf1.o(x$fpl$printf1)
    x$fpl$retnan                             0x08004f8c   Section      100  retnan.o(x$fpl$retnan)
    x$fpl$scalbn                             0x08004ff0   Section       92  scalbn.o(x$fpl$scalbn)
    x$fpl$scanf1                             0x0800504c   Section        4  scanf1.o(x$fpl$scanf1)
    x$fpl$scanf2                             0x08005050   Section        8  scanf2.o(x$fpl$scanf2)
    x$fpl$trapveneer                         0x08005058   Section       48  trapv.o(x$fpl$trapveneer)
    maptable                                 0x08005088   Data          17  __printf_flags_ss_wp.o(.constdata)
    .constdata                               0x08005088   Section       17  __printf_flags_ss_wp.o(.constdata)
    x$fpl$usenofp                            0x08005088   Section        0  usenofp.o(x$fpl$usenofp)
    tenpwrs_x                                0x0800509c   Data          60  bigflt0.o(.constdata)
    .constdata                               0x0800509c   Section      148  bigflt0.o(.constdata)
    tenpwrs_i                                0x080050d8   Data          64  bigflt0.o(.constdata)
    .L.str.12                                0x0800595a   Data          35  usart.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x0800595a   Section        0  usart.o(.rodata.str1.1)
    .L.str.5                                 0x0800597d   Data          25  usart.o(.rodata.str1.1)
    .L.str.3                                 0x08005996   Data          25  usart.o(.rodata.str1.1)
    .L.str.14                                0x080059af   Data          24  usart.o(.rodata.str1.1)
    .L.str.9                                 0x080059c7   Data          24  usart.o(.rodata.str1.1)
    .L.str.16                                0x080059df   Data          24  usart.o(.rodata.str1.1)
    .L.str.11                                0x080059f7   Data          24  usart.o(.rodata.str1.1)
    .L.str.15                                0x08005a0f   Data          24  usart.o(.rodata.str1.1)
    .L.str.10                                0x08005a27   Data          24  usart.o(.rodata.str1.1)
    .L.str.8                                 0x08005a3f   Data          30  usart.o(.rodata.str1.1)
    .L.str.18                                0x08005a5d   Data          34  usart.o(.rodata.str1.1)
    .L.str.7                                 0x08005a7f   Data          33  usart.o(.rodata.str1.1)
    .L.str.1                                 0x08005aa0   Data          24  usart.o(.rodata.str1.1)
    c$$dinf                                  0x08005ae4   Section        8  fpconst.o(c$$dinf)
    c$$dmax                                  0x08005aec   Section        8  fpconst.o(c$$dmax)
    locale$$data                             0x08005af4   Section      272  lc_ctype_c.o(locale$$data)
    __lcctype_c_name                         0x08005af8   Data           2  lc_ctype_c.o(locale$$data)
    __lcctype_c_start                        0x08005b00   Data           0  lc_ctype_c.o(locale$$data)
    __lcctype_c_end                          0x08005c04   Data           0  lc_ctype_c.o(locale$$data)
    locale$$data                             0x08005c04   Section       28  lc_numeric_c.o(locale$$data)
    __lcnum_c_name                           0x08005c08   Data           2  lc_numeric_c.o(locale$$data)
    __lcnum_c_start                          0x08005c10   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_point                          0x08005c1c   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_thousands                      0x08005c1e   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_grouping                       0x08005c1f   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_end                            0x08005c20   Data           0  lc_numeric_c.o(locale$$data)
    EXTI2_IRQHandler.last_tim2_count         0x20000000   Data           2  encoder.o(.data.EXTI2_IRQHandler.last_tim2_count)
    [Anonymous Symbol]                       0x20000000   Section        0  encoder.o(.data.EXTI2_IRQHandler.last_tim2_count)
    .bss                                     0x20000008   Section       96  libspace.o(.bss)
    TIM2_IRQHandler.last_absolute_count      0x20000468   Data           4  encoder.o(.bss.TIM2_IRQHandler.last_absolute_count)
    [Anonymous Symbol]                       0x20000468   Section        0  encoder.o(.bss.TIM2_IRQHandler.last_absolute_count)
    USART1_RxCpltCallback.cmd_buffer         0x2000046c   Data          32  usart.o(.bss.USART1_RxCpltCallback.cmd_buffer)
    [Anonymous Symbol]                       0x2000046c   Section        0  usart.o(.bss.USART1_RxCpltCallback.cmd_buffer)
    USART1_RxCpltCallback.cmd_index          0x2000048c   Data           1  usart.o(.bss.USART1_RxCpltCallback.cmd_index)
    [Anonymous Symbol]                       0x2000048c   Section        0  usart.o(.bss.USART1_RxCpltCallback.cmd_index)
    encoder                                  0x20000490   Data          24  encoder.o(.bss.encoder)
    [Anonymous Symbol]                       0x20000490   Section        0  encoder.o(.bss.encoder)
    g_set_value                              0x200004a8   Data           4  usart.o(.bss.g_set_value)
    [Anonymous Symbol]                       0x200004a8   Section        0  usart.o(.bss.g_set_value)
    pwmPeriod                                0x200004d4   Data           2  pwm.o(.bss.pwmPeriod)
    [Anonymous Symbol]                       0x200004d4   Section        0  pwm.o(.bss.pwmPeriod)
    Heap_Mem                                 0x200004d8   Data         512  startup_stm32f10x_md.o(HEAP)
    HEAP                                     0x200004d8   Section      512  startup_stm32f10x_md.o(HEAP)
    Stack_Mem                                0x200006d8   Data        1024  startup_stm32f10x_md.o(STACK)
    STACK                                    0x200006d8   Section     1024  startup_stm32f10x_md.o(STACK)
    __initial_sp                             0x20000ad8   Data           0  startup_stm32f10x_md.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$~IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __fp_init_empty                          0x00000000   Number         0  fpinit_empty.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __arm_relocate_pie_                       - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_wc                                - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x000000ec   Number         0  startup_stm32f10x_md.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f10x_md.o(RESET)
    __Vectors_End                            0x080000ec   Data           0  startup_stm32f10x_md.o(RESET)
    __main                                   0x080000ed   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x080000f5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x080000f5   Thumb Code    84  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x080000f5   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_loop                       0x080000ff   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x08000151   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_null                       0x0800016d   Thumb Code     2  __scatter.o(!!handler_null)
    __scatterload_zeroinit                   0x08000171   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_f                                0x0800018d   Thumb Code     0  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    _printf_percent                          0x0800018d   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_d                                0x08000193   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_percent_end                      0x08000199   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x0800019d   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x0800019f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_heap_1                     0x0800019f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_common                  0x0800019f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_preinit_1                  0x0800019f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000006)
    __rt_lib_init_rand_1                     0x0800019f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000010)
    __rt_lib_init_relocate_pie_1             0x0800019f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_user_alloc_1               0x0800019f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_lc_collate_1               0x080001a5   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_ctype_2                 0x080001a5   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000014)
    __rt_lib_init_lc_ctype_1                 0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_monetary_1              0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_numeric_2               0x080001b1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000018)
    __rt_lib_init_alloca_1                   0x080001bb   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_argv_1                     0x080001bb   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_atexit_1                   0x080001bb   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_clock_1                    0x080001bb   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_cpp_1                      0x080001bb   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000034)
    __rt_lib_init_exceptions_1               0x080001bb   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_fp_trap_1                  0x080001bb   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_getenv_1                   0x080001bb   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_init_lc_numeric_1               0x080001bb   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_lc_time_1                  0x080001bb   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_return                     0x080001bb   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000035)
    __rt_lib_init_signal_1                   0x080001bb   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_stdio_1                    0x080001bb   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000027)
    __rt_lib_shutdown                        0x080001bd   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x080001bf   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x080001bf   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x080001bf   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x080001bf   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x080001bf   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x080001bf   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x080001bf   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x080001c1   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x080001c1   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x080001c1   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x080001c7   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x080001c7   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x080001cb   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x080001cb   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x080001d3   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x080001d5   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x080001d5   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x080001d9   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x080001e1   Thumb Code     8  startup_stm32f10x_md.o(.text)
    ADC1_2_IRQHandler                        0x080001fb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    CAN1_RX1_IRQHandler                      0x080001fb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    CAN1_SCE_IRQHandler                      0x080001fb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel1_IRQHandler                 0x080001fb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel2_IRQHandler                 0x080001fb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel3_IRQHandler                 0x080001fb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel4_IRQHandler                 0x080001fb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel5_IRQHandler                 0x080001fb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel6_IRQHandler                 0x080001fb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    DMA1_Channel7_IRQHandler                 0x080001fb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI15_10_IRQHandler                     0x080001fb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI1_IRQHandler                         0x080001fb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI3_IRQHandler                         0x080001fb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI4_IRQHandler                         0x080001fb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    EXTI9_5_IRQHandler                       0x080001fb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    FLASH_IRQHandler                         0x080001fb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C1_ER_IRQHandler                       0x080001fb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C1_EV_IRQHandler                       0x080001fb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C2_ER_IRQHandler                       0x080001fb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    I2C2_EV_IRQHandler                       0x080001fb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    PVD_IRQHandler                           0x080001fb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    RCC_IRQHandler                           0x080001fb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    RTCAlarm_IRQHandler                      0x080001fb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    RTC_IRQHandler                           0x080001fb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    SPI1_IRQHandler                          0x080001fb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    SPI2_IRQHandler                          0x080001fb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TAMPER_IRQHandler                        0x080001fb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_BRK_IRQHandler                      0x080001fb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_CC_IRQHandler                       0x080001fb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x080001fb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM1_UP_IRQHandler                       0x080001fb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    TIM4_IRQHandler                          0x080001fb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USART2_IRQHandler                        0x080001fb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USART3_IRQHandler                        0x080001fb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USBWakeUp_IRQHandler                     0x080001fb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x080001fb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x080001fb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    WWDG_IRQHandler                          0x080001fb   Thumb Code     0  startup_stm32f10x_md.o(.text)
    __user_initial_stackheap                 0x080001fd   Thumb Code     0  startup_stm32f10x_md.o(.text)
    __2snprintf                              0x08000221   Thumb Code    58  __2snprintf.o(.text)
    _printf_int_dec                          0x08000261   Thumb Code   104  _printf_dec.o(.text)
    __printf                                 0x080002d9   Thumb Code   388  __printf_flags_ss_wp.o(.text)
    strtol                                   0x08000461   Thumb Code   112  strtol.o(.text)
    memcmp                                   0x080004d1   Thumb Code    88  memcmp.o(.text)
    __aeabi_memclr4                          0x08000529   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x08000529   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x08000529   Thumb Code     0  rt_memclr_w.o(.text)
    _memset_w                                0x0800052d   Thumb Code    74  rt_memclr_w.o(.text)
    strcmp                                   0x08000579   Thumb Code   128  strcmpv7m.o(.text)
    __use_two_region_memory                  0x080005f9   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x080005fb   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x080005fd   Thumb Code     2  heapauxi.o(.text)
    __rt_ctype_table                         0x08000601   Thumb Code    16  rt_ctype_table.o(.text)
    __aeabi_errno_addr                       0x08000611   Thumb Code     8  rt_errno_addr_intlibspace.o(.text)
    __errno$intlibspace                      0x08000611   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __rt_errno_addr$intlibspace              0x08000611   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __read_errno                             0x08000619   Thumb Code    10  _rserrno.o(.text)
    __set_errno                              0x08000623   Thumb Code    12  _rserrno.o(.text)
    _printf_int_common                       0x0800062f   Thumb Code   178  _printf_intcommon.o(.text)
    _printf_fp_dec_real                      0x08000891   Thumb Code   620  _printf_fp_dec.o(.text)
    _printf_char_common                      0x08000b07   Thumb Code    32  _printf_char_common.o(.text)
    _snputc                                  0x08000b2d   Thumb Code    16  _snputc.o(.text)
    _strtoul                                 0x08000b3d   Thumb Code   158  _strtoul.o(.text)
    __strtof_int                             0x08000bdb   Thumb Code     8  strtof.o(.text)
    __user_libspace                          0x08000be5   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08000be5   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08000be5   Thumb Code     0  libspace.o(.text)
    __rt_locale                              0x08000bed   Thumb Code     8  rt_locale_intlibspace.o(.text)
    _ll_udiv10                               0x08000bf5   Thumb Code   138  lludiv10.o(.text)
    _printf_fp_infnan                        0x08000c81   Thumb Code   112  _printf_fp_infnan.o(.text)
    _chval                                   0x08000d01   Thumb Code    28  _chval.o(.text)
    __strtod_int                             0x08000d61   Thumb Code    94  strtod.o(.text)
    _btod_etento                             0x08000dcd   Thumb Code   224  bigflt0.o(.text)
    __user_setup_stackheap                   0x08000eb1   Thumb Code    74  sys_stackheap_outer.o(.text)
    isspace                                  0x08000efb   Thumb Code    18  isspace.o(.text)
    _sgetc                                   0x08000f0d   Thumb Code    30  _sgetc.o(.text)
    _sbackspace                              0x08000f2b   Thumb Code    34  _sgetc.o(.text)
    exit                                     0x08000f4d   Thumb Code    18  exit.o(.text)
    _scanf_really_real                       0x080011ad   Thumb Code   684  scanf_fp.o(.text)
    __aeabi_llsl                             0x08001459   Thumb Code     0  llshl.o(.text)
    _ll_shift_l                              0x08001459   Thumb Code    38  llshl.o(.text)
    _sys_exit                                0x08001481   Thumb Code     8  sys_exit.o(.text)
    _scanf_really_hex_real                   0x0800148d   Thumb Code   786  scanf_hexfp.o(.text)
    _scanf_really_infnan                     0x080017ad   Thumb Code   292  scanf_infnan.o(.text)
    __I$use$semihosting                      0x080018e1   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x080018e1   Thumb Code     2  use_no_semi.o(.text)
    __semihosting_library_function           0x080018e3   Thumb Code     0  indicate_semi.o(.text)
    BusFault_Handler                         0x080018e5   Thumb Code     2  stm32f10x_it.o(.text.BusFault_Handler)
    DebugMon_Handler                         0x080018e9   Thumb Code     2  stm32f10x_it.o(.text.DebugMon_Handler)
    EXTI0_IRQHandler                         0x080018ed   Thumb Code    84  encoder.o(.text.EXTI0_IRQHandler)
    EXTI2_IRQHandler                         0x08001941   Thumb Code   104  encoder.o(.text.EXTI2_IRQHandler)
    EXTI_ClearITPendingBit                   0x080019a9   Thumb Code    12  stm32f10x_exti.o(.text.EXTI_ClearITPendingBit)
    EXTI_GetITStatus                         0x080019b5   Thumb Code    30  stm32f10x_exti.o(.text.EXTI_GetITStatus)
    EXTI_Init                                0x080019d5   Thumb Code   104  stm32f10x_exti.o(.text.EXTI_Init)
    Encoder_GetData                          0x08001a3d   Thumb Code    56  encoder.o(.text.Encoder_GetData)
    Encoder_Init                             0x08001a75   Thumb Code   224  encoder.o(.text.Encoder_Init)
    GPIO_EXTILineConfig                      0x08001b55   Thumb Code    54  stm32f10x_gpio.o(.text.GPIO_EXTILineConfig)
    GPIO_Init                                0x08001b8d   Thumb Code   188  stm32f10x_gpio.o(.text.GPIO_Init)
    GPIO_ReadInputDataBit                    0x08001c49   Thumb Code    10  stm32f10x_gpio.o(.text.GPIO_ReadInputDataBit)
    GPIO_SetPin                              0x08001c55   Thumb Code     4  gpio_driver.o(.text.GPIO_SetPin)
    GPIO_WriteBit                            0x08001c59   Thumb Code    12  stm32f10x_gpio.o(.text.GPIO_WriteBit)
    HardFault_Handler                        0x08001c65   Thumb Code     2  stm32f10x_it.o(.text.HardFault_Handler)
    MemManage_Handler                        0x08001c69   Thumb Code     2  stm32f10x_it.o(.text.MemManage_Handler)
    MotorControl_Init                        0x08001c6d   Thumb Code    42  motor_control.o(.text.MotorControl_Init)
    MotorControl_SetSpeed                    0x08001c99   Thumb Code     6  motor_control.o(.text.MotorControl_SetSpeed)
    MotorControl_Update                      0x08001ca1   Thumb Code    38  motor_control.o(.text.MotorControl_Update)
    NMI_Handler                              0x08001cc9   Thumb Code     2  stm32f10x_it.o(.text.NMI_Handler)
    NVIC_Init                                0x08001ccd   Thumb Code    94  misc.o(.text.NVIC_Init)
    OLED_I2C_SendByte                        0x08001d2d   Thumb Code    92  oled.o(.text.OLED_I2C_SendByte)
    OLED_Init                                0x08001d89   Thumb Code   284  oled.o(.text.OLED_Init)
    OLED_ShowFloatNum                        0x08001ea5   Thumb Code   676  oled.o(.text.OLED_ShowFloatNum)
    OLED_ShowImage                           0x08002149   Thumb Code   298  oled.o(.text.OLED_ShowImage)
    OLED_ShowSignedNum                       0x08002275   Thumb Code   312  oled.o(.text.OLED_ShowSignedNum)
    OLED_Update                              0x080023ad   Thumb Code    54  oled.o(.text.OLED_Update)
    OLED_WriteCommand                        0x080023e5   Thumb Code   178  oled.o(.text.OLED_WriteCommand)
    OLED_WriteData                           0x08002499   Thumb Code   132  oled.o(.text.OLED_WriteData)
    PID_Init                                 0x0800251d   Thumb Code    22  pid.o(.text.PID_Init)
    PID_SetTarget                            0x08002535   Thumb Code    10  pid.o(.text.PID_SetTarget)
    PID_Update                               0x08002541   Thumb Code   124  pid.o(.text.PID_Update)
    PWM_Init                                 0x080025bd   Thumb Code   274  pwm.o(.text.PWM_Init)
    PWM_SetDutyCycle                         0x080026d1   Thumb Code    86  pwm.o(.text.PWM_SetDutyCycle)
    PWM_Start                                0x08002729   Thumb Code    30  pwm.o(.text.PWM_Start)
    PendSV_Handler                           0x08002749   Thumb Code     2  stm32f10x_it.o(.text.PendSV_Handler)
    RCC_APB1PeriphClockCmd                   0x0800274d   Thumb Code    24  stm32f10x_rcc.o(.text.RCC_APB1PeriphClockCmd)
    RCC_APB2PeriphClockCmd                   0x08002765   Thumb Code    24  stm32f10x_rcc.o(.text.RCC_APB2PeriphClockCmd)
    SVC_Handler                              0x0800277d   Thumb Code     2  stm32f10x_it.o(.text.SVC_Handler)
    Show_Encoder                             0x08002781   Thumb Code   132  encoder.o(.text.Show_Encoder)
    SysTick_Handler                          0x08002805   Thumb Code     2  stm32f10x_it.o(.text.SysTick_Handler)
    SystemInit                               0x08002809   Thumb Code   272  system_stm32f10x.o(.text.SystemInit)
    TIM2_IRQHandler                          0x08002919   Thumb Code    78  encoder.o(.text.TIM2_IRQHandler)
    TIM3_Config                              0x08002969   Thumb Code   116  timer.o(.text.TIM3_Config)
    TIM3_IRQHandler                          0x080029dd   Thumb Code    48  timer.o(.text.TIM3_IRQHandler)
    TIM_ARRPreloadConfig                     0x08002a0d   Thumb Code    20  stm32f10x_tim.o(.text.TIM_ARRPreloadConfig)
    TIM_ClearITPendingBit                    0x08002a21   Thumb Code     6  stm32f10x_tim.o(.text.TIM_ClearITPendingBit)
    TIM_Cmd                                  0x08002a29   Thumb Code    18  stm32f10x_tim.o(.text.TIM_Cmd)
    TIM_CtrlPWMOutputs                       0x08002a3d   Thumb Code    24  stm32f10x_tim.o(.text.TIM_CtrlPWMOutputs)
    TIM_GetCounter                           0x08002a55   Thumb Code     4  stm32f10x_tim.o(.text.TIM_GetCounter)
    TIM_GetITStatus                          0x08002a59   Thumb Code    24  stm32f10x_tim.o(.text.TIM_GetITStatus)
    TIM_ITConfig                             0x08002a71   Thumb Code    16  stm32f10x_tim.o(.text.TIM_ITConfig)
    TIM_OC1Init                              0x08002a81   Thumb Code   112  stm32f10x_tim.o(.text.TIM_OC1Init)
    TIM_OC1PreloadConfig                     0x08002af1   Thumb Code    12  stm32f10x_tim.o(.text.TIM_OC1PreloadConfig)
    TIM_OC2Init                              0x08002afd   Thumb Code   116  stm32f10x_tim.o(.text.TIM_OC2Init)
    TIM_OC2PreloadConfig                     0x08002b71   Thumb Code    14  stm32f10x_tim.o(.text.TIM_OC2PreloadConfig)
    TIM_OC3Init                              0x08002b81   Thumb Code   116  stm32f10x_tim.o(.text.TIM_OC3Init)
    TIM_OC3PreloadConfig                     0x08002bf5   Thumb Code    12  stm32f10x_tim.o(.text.TIM_OC3PreloadConfig)
    TIM_OC4Init                              0x08002c01   Thumb Code    92  stm32f10x_tim.o(.text.TIM_OC4Init)
    TIM_OC4PreloadConfig                     0x08002c5d   Thumb Code    14  stm32f10x_tim.o(.text.TIM_OC4PreloadConfig)
    TIM_SetCompare1                          0x08002c6d   Thumb Code     4  stm32f10x_tim.o(.text.TIM_SetCompare1)
    TIM_SetCompare4                          0x08002c71   Thumb Code     6  stm32f10x_tim.o(.text.TIM_SetCompare4)
    TIM_TimeBaseInit                         0x08002c79   Thumb Code   170  stm32f10x_tim.o(.text.TIM_TimeBaseInit)
    USART1_IRQHandler                        0x08002d25   Thumb Code    84  usart.o(.text.USART1_IRQHandler)
    USART1_RxCpltCallback                    0x08002d79   Thumb Code  1040  usart.o(.text.USART1_RxCpltCallback)
    USART1_SendString                        0x08003221   Thumb Code    50  usart.o(.text.USART1_SendString)
    USART_ClearITPendingBit                  0x08003255   Thumb Code    14  stm32f10x_usart.o(.text.USART_ClearITPendingBit)
    USART_GetFlagStatus                      0x08003265   Thumb Code    10  stm32f10x_usart.o(.text.USART_GetFlagStatus)
    USART_GetITStatus                        0x08003271   Thumb Code    42  stm32f10x_usart.o(.text.USART_GetITStatus)
    USART_ITConfig                           0x0800329d   Thumb Code    46  stm32f10x_usart.o(.text.USART_ITConfig)
    USART_ReceiveData                        0x080032cd   Thumb Code     8  stm32f10x_usart.o(.text.USART_ReceiveData)
    USART_SendData                           0x080032d5   Thumb Code     8  stm32f10x_usart.o(.text.USART_SendData)
    UsageFault_Handler                       0x080032dd   Thumb Code     2  stm32f10x_it.o(.text.UsageFault_Handler)
    main                                     0x080032e1   Thumb Code   182  main.o(.text.main)
    _btod_d2e                                0x08003397   Thumb Code    62  btod.o(CL$$btod_d2e)
    _d2e_denorm_low                          0x080033d5   Thumb Code    70  btod.o(CL$$btod_d2e_denorm_low)
    _d2e_norm_op1                            0x0800341b   Thumb Code    96  btod.o(CL$$btod_d2e_norm_op1)
    __btod_div_common                        0x0800347d   Thumb Code   696  btod.o(CL$$btod_div_common)
    _e2d                                     0x080037b5   Thumb Code   122  btod.o(CL$$btod_e2d)
    _e2e                                     0x08003839   Thumb Code   220  btod.o(CL$$btod_e2e)
    _btod_ediv                               0x08003915   Thumb Code    42  btod.o(CL$$btod_ediv)
    _btod_edivd                              0x0800393f   Thumb Code    42  btod.o(CL$$btod_edivd)
    _btod_emul                               0x08003969   Thumb Code    42  btod.o(CL$$btod_emul)
    _btod_emuld                              0x08003993   Thumb Code    42  btod.o(CL$$btod_emuld)
    __btod_mult_common                       0x080039bd   Thumb Code   580  btod.o(CL$$btod_mult_common)
    __ARM_fpclassify                         0x08003c01   Thumb Code    40  fpclassify.o(i.__ARM_fpclassify)
    __mathlib_dbl_overflow                   0x08003c29   Thumb Code    14  dunder.o(i.__mathlib_dbl_overflow)
    __mathlib_dbl_underflow                  0x08003c39   Thumb Code    10  dunder.o(i.__mathlib_dbl_underflow)
    __mathlib_narrow                         0x08003c49   Thumb Code    16  narrow.o(i.__mathlib_narrow)
    __mathlib_tofloat                        0x08003c59   Thumb Code   184  narrow.o(i.__mathlib_tofloat)
    __support_ldexp                          0x08003d15   Thumb Code    22  ldexp.o(i.__support_ldexp)
    _is_digit                                0x08003d2b   Thumb Code    14  __printf_wp.o(i._is_digit)
    frexp                                    0x08003d39   Thumb Code    80  frexp.o(i.frexp)
    ldexp                                    0x08003d91   Thumb Code   116  ldexp.o(i.ldexp)
    round                                    0x08003e05   Thumb Code   136  round.o(i.round)
    strtof                                   0x08003e99   Thumb Code    20  strtof.o(i.strtof)
    _get_lc_ctype                            0x08003ead   Thumb Code    44  lc_ctype_c.o(locale$$code)
    _get_lc_numeric                          0x08003ed9   Thumb Code    44  lc_numeric_c.o(locale$$code)
    __aeabi_d2f                              0x08003f05   Thumb Code     0  d2f.o(x$fpl$d2f)
    _d2f                                     0x08003f05   Thumb Code    98  d2f.o(x$fpl$d2f)
    __aeabi_dadd                             0x08003f69   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    _dadd                                    0x08003f69   Thumb Code   332  daddsub_clz.o(x$fpl$dadd)
    __fpl_dcheck_NaN1                        0x080040b9   Thumb Code    10  dcheck1.o(x$fpl$dcheck1)
    __fpl_dcmp_Inf                           0x080040c9   Thumb Code    24  dcmpi.o(x$fpl$dcmpinf)
    __aeabi_cdcmpeq                          0x080040e1   Thumb Code     0  deqf.o(x$fpl$deqf)
    _dcmpeq                                  0x080040e1   Thumb Code   120  deqf.o(x$fpl$deqf)
    __aeabi_d2uiz                            0x08004159   Thumb Code     0  dfixu.o(x$fpl$dfixu)
    _dfixu                                   0x08004159   Thumb Code    90  dfixu.o(x$fpl$dfixu)
    __aeabi_ui2d                             0x080041b3   Thumb Code     0  dflt_clz.o(x$fpl$dfltu)
    _dfltu                                   0x080041b3   Thumb Code    38  dflt_clz.o(x$fpl$dfltu)
    __aeabi_cdcmpge                          0x080041d9   Thumb Code     0  dgeqf.o(x$fpl$dgeqf)
    _dcmpge                                  0x080041d9   Thumb Code   120  dgeqf.o(x$fpl$dgeqf)
    __aeabi_cdcmple                          0x08004251   Thumb Code     0  dleqf.o(x$fpl$dleqf)
    _dcmple                                  0x08004251   Thumb Code   120  dleqf.o(x$fpl$dleqf)
    __fpl_dcmple_InfNaN                      0x080042b3   Thumb Code     0  dleqf.o(x$fpl$dleqf)
    __aeabi_dmul                             0x080042c9   Thumb Code     0  dmul.o(x$fpl$dmul)
    _dmul                                    0x080042c9   Thumb Code   332  dmul.o(x$fpl$dmul)
    __fpl_dnaninf                            0x0800441d   Thumb Code   156  dnaninf.o(x$fpl$dnaninf)
    __fpl_dretinf                            0x080044b9   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_cdrcmple                         0x080044c5   Thumb Code     0  drleqf.o(x$fpl$drleqf)
    _drcmple                                 0x080044c5   Thumb Code   108  drleqf.o(x$fpl$drleqf)
    _drnd                                    0x08004531   Thumb Code   180  drnd.o(x$fpl$drnd)
    __aeabi_drsub                            0x080045e5   Thumb Code     0  daddsub_clz.o(x$fpl$drsb)
    _drsb                                    0x080045e5   Thumb Code    22  daddsub_clz.o(x$fpl$drsb)
    __aeabi_dsub                             0x080045fd   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    _dsub                                    0x080045fd   Thumb Code   472  daddsub_clz.o(x$fpl$dsub)
    __aeabi_f2d                              0x080047d9   Thumb Code     0  f2d.o(x$fpl$f2d)
    _f2d                                     0x080047d9   Thumb Code    86  f2d.o(x$fpl$f2d)
    __aeabi_fadd                             0x08004831   Thumb Code     0  faddsub_clz.o(x$fpl$fadd)
    _fadd                                    0x08004831   Thumb Code   196  faddsub_clz.o(x$fpl$fadd)
    __aeabi_dcmpeq                           0x080048f5   Thumb Code     0  dcmp.o(x$fpl$fcmp)
    _deq                                     0x080048f5   Thumb Code    14  dcmp.o(x$fpl$fcmp)
    _dneq                                    0x08004903   Thumb Code    14  dcmp.o(x$fpl$fcmp)
    __aeabi_dcmpgt                           0x08004911   Thumb Code     0  dcmp.o(x$fpl$fcmp)
    _dgr                                     0x08004911   Thumb Code    14  dcmp.o(x$fpl$fcmp)
    __aeabi_dcmpge                           0x0800491f   Thumb Code     0  dcmp.o(x$fpl$fcmp)
    _dgeq                                    0x0800491f   Thumb Code    14  dcmp.o(x$fpl$fcmp)
    __aeabi_dcmple                           0x0800492d   Thumb Code     0  dcmp.o(x$fpl$fcmp)
    _dleq                                    0x0800492d   Thumb Code    14  dcmp.o(x$fpl$fcmp)
    __aeabi_dcmplt                           0x0800493b   Thumb Code     0  dcmp.o(x$fpl$fcmp)
    _dls                                     0x0800493b   Thumb Code    14  dcmp.o(x$fpl$fcmp)
    __aeabi_fcmpeq                           0x08004949   Thumb Code     0  fcmp.o(x$fpl$fcmp)
    _feq                                     0x08004949   Thumb Code    14  fcmp.o(x$fpl$fcmp)
    _fneq                                    0x08004957   Thumb Code    14  fcmp.o(x$fpl$fcmp)
    __aeabi_fcmpgt                           0x08004965   Thumb Code     0  fcmp.o(x$fpl$fcmp)
    _fgr                                     0x08004965   Thumb Code    14  fcmp.o(x$fpl$fcmp)
    __aeabi_fcmpge                           0x08004973   Thumb Code     0  fcmp.o(x$fpl$fcmp)
    _fgeq                                    0x08004973   Thumb Code    14  fcmp.o(x$fpl$fcmp)
    __aeabi_fcmple                           0x08004981   Thumb Code     0  fcmp.o(x$fpl$fcmp)
    _fleq                                    0x08004981   Thumb Code    14  fcmp.o(x$fpl$fcmp)
    __aeabi_fcmplt                           0x0800498f   Thumb Code     0  fcmp.o(x$fpl$fcmp)
    _fls                                     0x0800498f   Thumb Code    14  fcmp.o(x$fpl$fcmp)
    __fpl_fcmp_Inf                           0x0800499d   Thumb Code    24  fcmpi.o(x$fpl$fcmpinf)
    __aeabi_fdiv                             0x080049b5   Thumb Code     0  fdiv.o(x$fpl$fdiv)
    _fdiv                                    0x080049b5   Thumb Code   384  fdiv.o(x$fpl$fdiv)
    __aeabi_cfcmpeq                          0x08004b39   Thumb Code     0  feqf.o(x$fpl$feqf)
    _fcmpeq                                  0x08004b39   Thumb Code   104  feqf.o(x$fpl$feqf)
    __aeabi_f2iz                             0x08004ba1   Thumb Code     0  ffix.o(x$fpl$ffix)
    _ffix                                    0x08004ba1   Thumb Code    54  ffix.o(x$fpl$ffix)
    __aeabi_i2f                              0x08004bd9   Thumb Code     0  fflt_clz.o(x$fpl$fflt)
    _fflt                                    0x08004bd9   Thumb Code    48  fflt_clz.o(x$fpl$fflt)
    __aeabi_ui2f                             0x08004c09   Thumb Code     0  fflt_clz.o(x$fpl$ffltu)
    _ffltu                                   0x08004c09   Thumb Code    38  fflt_clz.o(x$fpl$ffltu)
    _fcmpge                                  0x08004c31   Thumb Code   104  fgeqf.o(x$fpl$fgeqf)
    __aeabi_cfcmple                          0x08004c99   Thumb Code     0  fleqf.o(x$fpl$fleqf)
    _fcmple                                  0x08004c99   Thumb Code   104  fleqf.o(x$fpl$fleqf)
    __fpl_fcmple_InfNaN                      0x08004ceb   Thumb Code     0  fleqf.o(x$fpl$fleqf)
    __aeabi_fmul                             0x08004d01   Thumb Code     0  fmul.o(x$fpl$fmul)
    _fmul                                    0x08004d01   Thumb Code   258  fmul.o(x$fpl$fmul)
    __fpl_fnaninf                            0x08004e03   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    __fpl_fretinf                            0x08004e8f   Thumb Code    10  fretinf.o(x$fpl$fretinf)
    __aeabi_fsub                             0x08004e99   Thumb Code     0  faddsub_clz.o(x$fpl$fsub)
    _fsub                                    0x08004e99   Thumb Code   234  faddsub_clz.o(x$fpl$fsub)
    __ieee_status                            0x08004f83   Thumb Code     6  istatus.o(x$fpl$ieeestatus)
    _printf_fp_dec                           0x08004f89   Thumb Code     4  printf1.o(x$fpl$printf1)
    __fpl_return_NaN                         0x08004f8d   Thumb Code   100  retnan.o(x$fpl$retnan)
    __ARM_scalbn                             0x08004ff1   Thumb Code    92  scalbn.o(x$fpl$scalbn)
    _scanf_real                              0x0800504d   Thumb Code     4  scanf1.o(x$fpl$scanf1)
    _scanf_hex_real                          0x08005051   Thumb Code     4  scanf2.o(x$fpl$scanf2)
    _scanf_infnan                            0x08005055   Thumb Code     4  scanf2.o(x$fpl$scanf2)
    __fpl_cmpreturn                          0x08005059   Thumb Code    48  trapv.o(x$fpl$trapveneer)
    __I$use$fp                               0x08005088   Number         0  usenofp.o(x$fpl$usenofp)
    OLED_F6x8                                0x08005130   Data         570  oled_data.o(.rodata.OLED_F6x8)
    OLED_F8x16                               0x0800536a   Data        1520  oled_data.o(.rodata.OLED_F8x16)
    Region$$Table$$Base                      0x08005ac4   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08005ae4   Number         0  anon$$obj.o(Region$$Table)
    __aeabi_HUGE_VAL                         0x08005ae4   Data           0  fpconst.o(c$$dinf)
    __aeabi_HUGE_VALL                        0x08005ae4   Data           0  fpconst.o(c$$dinf)
    __aeabi_INFINITY                         0x08005ae4   Data           0  fpconst.o(c$$dinf)
    __dInf                                   0x08005ae4   Data           0  fpconst.o(c$$dinf)
    __huge_val                               0x08005ae4   Data           0  fpconst.o(c$$dinf)
    __dbl_max                                0x08005aec   Data           0  fpconst.o(c$$dmax)
    __ctype                                  0x08005b01   Data           0  lc_ctype_c.o(locale$$data)
    SystemCoreClock                          0x20000004   Data           4  system_stm32f10x.o(.data.SystemCoreClock)
    __libspace_start                         0x20000008   Data          96  libspace.o(.bss)
    OLED_DisplayBuf                          0x20000068   Data        1024  oled.o(.bss.OLED_DisplayBuf)
    __temporary_stack_top$libspace           0x20000068   Data           0  libspace.o(.bss)
    motor                                    0x200004ac   Data          40  main.o(.bss.motor)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080000ed

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00005c28, Max: 0x00010000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00005c20, Max: 0x00010000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000000ec   Data   RO         1124    RESET               startup_stm32f10x_md.o
    0x080000ec   0x080000ec   0x00000008   Code   RO         1488  * !!!main             c_w.l(__main.o)
    0x080000f4   0x080000f4   0x0000005c   Code   RO         1984    !!!scatter          c_w.l(__scatter.o)
    0x08000150   0x08000150   0x0000001a   Code   RO         1988    !!handler_copy      c_w.l(__scatter_copy.o)
    0x0800016a   0x0800016a   0x00000002   PAD
    0x0800016c   0x0800016c   0x00000002   Code   RO         1985    !!handler_null      c_w.l(__scatter.o)
    0x0800016e   0x0800016e   0x00000002   PAD
    0x08000170   0x08000170   0x0000001c   Code   RO         1990    !!handler_zi        c_w.l(__scatter_zi.o)
    0x0800018c   0x0800018c   0x00000000   Code   RO         1477    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x0800018c   0x0800018c   0x00000006   Code   RO         1476    .ARM.Collect$$_printf_percent$$00000003  c_w.l(_printf_f.o)
    0x08000192   0x08000192   0x00000006   Code   RO         1475    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x08000198   0x08000198   0x00000004   Code   RO         1631    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x0800019c   0x0800019c   0x00000002   Code   RO         1798    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x0800019e   0x0800019e   0x00000000   Code   RO         1800    .ARM.Collect$$libinit$$00000002  c_w.l(libinit2.o)
    0x0800019e   0x0800019e   0x00000000   Code   RO         1802    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x0800019e   0x0800019e   0x00000000   Code   RO         1804    .ARM.Collect$$libinit$$00000006  c_w.l(libinit2.o)
    0x0800019e   0x0800019e   0x00000000   Code   RO         1807    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x0800019e   0x0800019e   0x00000000   Code   RO         1809    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x0800019e   0x0800019e   0x00000000   Code   RO         1811    .ARM.Collect$$libinit$$00000010  c_w.l(libinit2.o)
    0x0800019e   0x0800019e   0x00000006   Code   RO         1812    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x080001a4   0x080001a4   0x00000000   Code   RO         1814    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x080001a4   0x080001a4   0x0000000c   Code   RO         1815    .ARM.Collect$$libinit$$00000014  c_w.l(libinit2.o)
    0x080001b0   0x080001b0   0x00000000   Code   RO         1816    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x080001b0   0x080001b0   0x00000000   Code   RO         1818    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x080001b0   0x080001b0   0x0000000a   Code   RO         1819    .ARM.Collect$$libinit$$00000018  c_w.l(libinit2.o)
    0x080001ba   0x080001ba   0x00000000   Code   RO         1820    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x080001ba   0x080001ba   0x00000000   Code   RO         1822    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x080001ba   0x080001ba   0x00000000   Code   RO         1824    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x080001ba   0x080001ba   0x00000000   Code   RO         1826    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x080001ba   0x080001ba   0x00000000   Code   RO         1828    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x080001ba   0x080001ba   0x00000000   Code   RO         1830    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x080001ba   0x080001ba   0x00000000   Code   RO         1832    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x080001ba   0x080001ba   0x00000000   Code   RO         1834    .ARM.Collect$$libinit$$00000027  c_w.l(libinit2.o)
    0x080001ba   0x080001ba   0x00000000   Code   RO         1838    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x080001ba   0x080001ba   0x00000000   Code   RO         1840    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x080001ba   0x080001ba   0x00000000   Code   RO         1842    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x080001ba   0x080001ba   0x00000000   Code   RO         1844    .ARM.Collect$$libinit$$00000034  c_w.l(libinit2.o)
    0x080001ba   0x080001ba   0x00000002   Code   RO         1845    .ARM.Collect$$libinit$$00000035  c_w.l(libinit2.o)
    0x080001bc   0x080001bc   0x00000002   Code   RO         1920    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x080001be   0x080001be   0x00000000   Code   RO         1935    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x080001be   0x080001be   0x00000000   Code   RO         1937    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x080001be   0x080001be   0x00000000   Code   RO         1940    .ARM.Collect$$libshutdown$$00000007  c_w.l(libshutdown2.o)
    0x080001be   0x080001be   0x00000000   Code   RO         1943    .ARM.Collect$$libshutdown$$0000000A  c_w.l(libshutdown2.o)
    0x080001be   0x080001be   0x00000000   Code   RO         1945    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x080001be   0x080001be   0x00000000   Code   RO         1948    .ARM.Collect$$libshutdown$$0000000F  c_w.l(libshutdown2.o)
    0x080001be   0x080001be   0x00000002   Code   RO         1949    .ARM.Collect$$libshutdown$$00000010  c_w.l(libshutdown2.o)
    0x080001c0   0x080001c0   0x00000000   Code   RO         1558    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x080001c0   0x080001c0   0x00000000   Code   RO         1700    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x080001c0   0x080001c0   0x00000006   Code   RO         1712    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x080001c6   0x080001c6   0x00000000   Code   RO         1702    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x080001c6   0x080001c6   0x00000004   Code   RO         1703    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x080001ca   0x080001ca   0x00000000   Code   RO         1705    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x080001ca   0x080001ca   0x00000008   Code   RO         1706    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x080001d2   0x080001d2   0x00000002   Code   RO         1850    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x080001d4   0x080001d4   0x00000000   Code   RO         1865    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x080001d4   0x080001d4   0x00000004   Code   RO         1866    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x080001d8   0x080001d8   0x00000006   Code   RO         1867    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x080001de   0x080001de   0x00000002   PAD
    0x080001e0   0x080001e0   0x00000040   Code   RO         1125    .text               startup_stm32f10x_md.o
    0x08000220   0x08000220   0x00000040   Code   RO         1447    .text               c_w.l(__2snprintf.o)
    0x08000260   0x08000260   0x00000078   Code   RO         1453    .text               c_w.l(_printf_dec.o)
    0x080002d8   0x080002d8   0x00000188   Code   RO         1472    .text               c_w.l(__printf_flags_ss_wp.o)
    0x08000460   0x08000460   0x00000070   Code   RO         1478    .text               c_w.l(strtol.o)
    0x080004d0   0x080004d0   0x00000058   Code   RO         1480    .text               c_w.l(memcmp.o)
    0x08000528   0x08000528   0x0000004e   Code   RO         1482    .text               c_w.l(rt_memclr_w.o)
    0x08000576   0x08000576   0x00000002   PAD
    0x08000578   0x08000578   0x00000080   Code   RO         1484    .text               c_w.l(strcmpv7m.o)
    0x080005f8   0x080005f8   0x00000006   Code   RO         1486    .text               c_w.l(heapauxi.o)
    0x080005fe   0x080005fe   0x00000002   PAD
    0x08000600   0x08000600   0x00000010   Code   RO         1559    .text               c_w.l(rt_ctype_table.o)
    0x08000610   0x08000610   0x00000008   Code   RO         1564    .text               c_w.l(rt_errno_addr_intlibspace.o)
    0x08000618   0x08000618   0x00000016   Code   RO         1566    .text               c_w.l(_rserrno.o)
    0x0800062e   0x0800062e   0x000000b2   Code   RO         1574    .text               c_w.l(_printf_intcommon.o)
    0x080006e0   0x080006e0   0x0000041c   Code   RO         1578    .text               c_w.l(_printf_fp_dec.o)
    0x08000afc   0x08000afc   0x00000030   Code   RO         1582    .text               c_w.l(_printf_char_common.o)
    0x08000b2c   0x08000b2c   0x00000010   Code   RO         1586    .text               c_w.l(_snputc.o)
    0x08000b3c   0x08000b3c   0x0000009e   Code   RO         1641    .text               c_w.l(_strtoul.o)
    0x08000bda   0x08000bda   0x00000008   Code   RO         1643    .text               c_w.l(strtof.o)
    0x08000be2   0x08000be2   0x00000002   PAD
    0x08000be4   0x08000be4   0x00000008   Code   RO         1696    .text               c_w.l(libspace.o)
    0x08000bec   0x08000bec   0x00000008   Code   RO         1717    .text               c_w.l(rt_locale_intlibspace.o)
    0x08000bf4   0x08000bf4   0x0000008a   Code   RO         1719    .text               c_w.l(lludiv10.o)
    0x08000c7e   0x08000c7e   0x00000002   PAD
    0x08000c80   0x08000c80   0x00000080   Code   RO         1724    .text               c_w.l(_printf_fp_infnan.o)
    0x08000d00   0x08000d00   0x0000001c   Code   RO         1732    .text               c_w.l(_chval.o)
    0x08000d1c   0x08000d1c   0x000000b0   Code   RO         1734    .text               c_w.l(strtod.o)
    0x08000dcc   0x08000dcc   0x000000e4   Code   RO         1736    .text               c_w.l(bigflt0.o)
    0x08000eb0   0x08000eb0   0x0000004a   Code   RO         1783    .text               c_w.l(sys_stackheap_outer.o)
    0x08000efa   0x08000efa   0x00000012   Code   RO         1785    .text               c_w.l(isspace.o)
    0x08000f0c   0x08000f0c   0x00000040   Code   RO         1787    .text               c_w.l(_sgetc.o)
    0x08000f4c   0x08000f4c   0x00000012   Code   RO         1791    .text               c_w.l(exit.o)
    0x08000f5e   0x08000f5e   0x00000002   PAD
    0x08000f60   0x08000f60   0x000004f8   Code   RO         1854    .text               c_w.l(scanf_fp.o)
    0x08001458   0x08001458   0x00000026   Code   RO         1871    .text               c_w.l(llshl.o)
    0x0800147e   0x0800147e   0x00000002   PAD
    0x08001480   0x08001480   0x0000000c   Code   RO         1902    .text               c_w.l(sys_exit.o)
    0x0800148c   0x0800148c   0x00000320   Code   RO         1910    .text               c_w.l(scanf_hexfp.o)
    0x080017ac   0x080017ac   0x00000134   Code   RO         1912    .text               c_w.l(scanf_infnan.o)
    0x080018e0   0x080018e0   0x00000002   Code   RO         1925    .text               c_w.l(use_no_semi.o)
    0x080018e2   0x080018e2   0x00000000   Code   RO         1927    .text               c_w.l(indicate_semi.o)
    0x080018e2   0x080018e2   0x00000002   PAD
    0x080018e4   0x080018e4   0x00000002   Code   RO         1209    .text.BusFault_Handler  stm32f10x_it.o
    0x080018e6   0x080018e6   0x00000002   PAD
    0x080018e8   0x080018e8   0x00000002   Code   RO         1215    .text.DebugMon_Handler  stm32f10x_it.o
    0x080018ea   0x080018ea   0x00000002   PAD
    0x080018ec   0x080018ec   0x00000054   Code   RO         1301    .text.EXTI0_IRQHandler  encoder.o
    0x08001940   0x08001940   0x00000068   Code   RO         1303    .text.EXTI2_IRQHandler  encoder.o
    0x080019a8   0x080019a8   0x0000000c   Code   RO          334    .text.EXTI_ClearITPendingBit  stm32f10x_exti.o
    0x080019b4   0x080019b4   0x0000001e   Code   RO          332    .text.EXTI_GetITStatus  stm32f10x_exti.o
    0x080019d2   0x080019d2   0x00000002   PAD
    0x080019d4   0x080019d4   0x00000068   Code   RO          322    .text.EXTI_Init     stm32f10x_exti.o
    0x08001a3c   0x08001a3c   0x00000038   Code   RO         1309    .text.Encoder_GetData  encoder.o
    0x08001a74   0x08001a74   0x000000e0   Code   RO         1307    .text.Encoder_Init  encoder.o
    0x08001b54   0x08001b54   0x00000036   Code   RO          486    .text.GPIO_EXTILineConfig  stm32f10x_gpio.o
    0x08001b8a   0x08001b8a   0x00000002   PAD
    0x08001b8c   0x08001b8c   0x000000bc   Code   RO          458    .text.GPIO_Init     stm32f10x_gpio.o
    0x08001c48   0x08001c48   0x0000000a   Code   RO          462    .text.GPIO_ReadInputDataBit  stm32f10x_gpio.o
    0x08001c52   0x08001c52   0x00000002   PAD
    0x08001c54   0x08001c54   0x00000004   Code   RO         1230    .text.GPIO_SetPin   gpio_driver.o
    0x08001c58   0x08001c58   0x0000000c   Code   RO          474    .text.GPIO_WriteBit  stm32f10x_gpio.o
    0x08001c64   0x08001c64   0x00000002   Code   RO         1205    .text.HardFault_Handler  stm32f10x_it.o
    0x08001c66   0x08001c66   0x00000002   PAD
    0x08001c68   0x08001c68   0x00000002   Code   RO         1207    .text.MemManage_Handler  stm32f10x_it.o
    0x08001c6a   0x08001c6a   0x00000002   PAD
    0x08001c6c   0x08001c6c   0x0000002a   Code   RO         1428    .text.MotorControl_Init  motor_control.o
    0x08001c96   0x08001c96   0x00000002   PAD
    0x08001c98   0x08001c98   0x00000006   Code   RO         1430    .text.MotorControl_SetSpeed  motor_control.o
    0x08001c9e   0x08001c9e   0x00000002   PAD
    0x08001ca0   0x08001ca0   0x00000026   Code   RO         1432    .text.MotorControl_Update  motor_control.o
    0x08001cc6   0x08001cc6   0x00000002   PAD
    0x08001cc8   0x08001cc8   0x00000002   Code   RO         1203    .text.NMI_Handler   stm32f10x_it.o
    0x08001cca   0x08001cca   0x00000002   PAD
    0x08001ccc   0x08001ccc   0x0000005e   Code   RO           84    .text.NVIC_Init     misc.o
    0x08001d2a   0x08001d2a   0x00000002   PAD
    0x08001d2c   0x08001d2c   0x0000005c   Code   RO         1348    .text.OLED_I2C_SendByte  oled.o
    0x08001d88   0x08001d88   0x0000011c   Code   RO         1354    .text.OLED_Init     oled.o
    0x08001ea4   0x08001ea4   0x000002a4   Code   RO         1390    .text.OLED_ShowFloatNum  oled.o
    0x08002148   0x08002148   0x0000012a   Code   RO         1378    .text.OLED_ShowImage  oled.o
    0x08002272   0x08002272   0x00000002   PAD
    0x08002274   0x08002274   0x00000138   Code   RO         1384    .text.OLED_ShowSignedNum  oled.o
    0x080023ac   0x080023ac   0x00000036   Code   RO         1358    .text.OLED_Update   oled.o
    0x080023e2   0x080023e2   0x00000002   PAD
    0x080023e4   0x080023e4   0x000000b2   Code   RO         1350    .text.OLED_WriteCommand  oled.o
    0x08002496   0x08002496   0x00000002   PAD
    0x08002498   0x08002498   0x00000084   Code   RO         1352    .text.OLED_WriteData  oled.o
    0x0800251c   0x0800251c   0x00000016   Code   RO         1324    .text.PID_Init      pid.o
    0x08002532   0x08002532   0x00000002   PAD
    0x08002534   0x08002534   0x0000000a   Code   RO         1326    .text.PID_SetTarget  pid.o
    0x0800253e   0x0800253e   0x00000002   PAD
    0x08002540   0x08002540   0x0000007c   Code   RO         1328    .text.PID_Update    pid.o
    0x080025bc   0x080025bc   0x00000112   Code   RO         1244    .text.PWM_Init      pwm.o
    0x080026ce   0x080026ce   0x00000002   PAD
    0x080026d0   0x080026d0   0x00000056   Code   RO         1246    .text.PWM_SetDutyCycle  pwm.o
    0x08002726   0x08002726   0x00000002   PAD
    0x08002728   0x08002728   0x0000001e   Code   RO         1248    .text.PWM_Start     pwm.o
    0x08002746   0x08002746   0x00000002   PAD
    0x08002748   0x08002748   0x00000002   Code   RO         1217    .text.PendSV_Handler  stm32f10x_it.o
    0x0800274a   0x0800274a   0x00000002   PAD
    0x0800274c   0x0800274c   0x00000018   Code   RO          665    .text.RCC_APB1PeriphClockCmd  stm32f10x_rcc.o
    0x08002764   0x08002764   0x00000018   Code   RO          663    .text.RCC_APB2PeriphClockCmd  stm32f10x_rcc.o
    0x0800277c   0x0800277c   0x00000002   Code   RO         1213    .text.SVC_Handler   stm32f10x_it.o
    0x0800277e   0x0800277e   0x00000002   PAD
    0x08002780   0x08002780   0x00000084   Code   RO         1311    .text.Show_Encoder  encoder.o
    0x08002804   0x08002804   0x00000002   Code   RO         1219    .text.SysTick_Handler  stm32f10x_it.o
    0x08002806   0x08002806   0x00000002   PAD
    0x08002808   0x08002808   0x00000110   Code   RO         1132    .text.SystemInit    system_stm32f10x.o
    0x08002918   0x08002918   0x0000004e   Code   RO         1305    .text.TIM2_IRQHandler  encoder.o
    0x08002966   0x08002966   0x00000002   PAD
    0x08002968   0x08002968   0x00000074   Code   RO         1289    .text.TIM3_Config   timer.o
    0x080029dc   0x080029dc   0x00000030   Code   RO         1291    .text.TIM3_IRQHandler  timer.o
    0x08002a0c   0x08002a0c   0x00000014   Code   RO          925    .text.TIM_ARRPreloadConfig  stm32f10x_tim.o
    0x08002a20   0x08002a20   0x00000006   Code   RO         1023    .text.TIM_ClearITPendingBit  stm32f10x_tim.o
    0x08002a26   0x08002a26   0x00000002   PAD
    0x08002a28   0x08002a28   0x00000012   Code   RO          885    .text.TIM_Cmd       stm32f10x_tim.o
    0x08002a3a   0x08002a3a   0x00000002   PAD
    0x08002a3c   0x08002a3c   0x00000018   Code   RO          887    .text.TIM_CtrlPWMOutputs  stm32f10x_tim.o
    0x08002a54   0x08002a54   0x00000004   Code   RO         1013    .text.TIM_GetCounter  stm32f10x_tim.o
    0x08002a58   0x08002a58   0x00000018   Code   RO         1021    .text.TIM_GetITStatus  stm32f10x_tim.o
    0x08002a70   0x08002a70   0x00000010   Code   RO          889    .text.TIM_ITConfig  stm32f10x_tim.o
    0x08002a80   0x08002a80   0x00000070   Code   RO          855    .text.TIM_OC1Init   stm32f10x_tim.o
    0x08002af0   0x08002af0   0x0000000c   Code   RO          933    .text.TIM_OC1PreloadConfig  stm32f10x_tim.o
    0x08002afc   0x08002afc   0x00000074   Code   RO          857    .text.TIM_OC2Init   stm32f10x_tim.o
    0x08002b70   0x08002b70   0x0000000e   Code   RO          935    .text.TIM_OC2PreloadConfig  stm32f10x_tim.o
    0x08002b7e   0x08002b7e   0x00000002   PAD
    0x08002b80   0x08002b80   0x00000074   Code   RO          859    .text.TIM_OC3Init   stm32f10x_tim.o
    0x08002bf4   0x08002bf4   0x0000000c   Code   RO          937    .text.TIM_OC3PreloadConfig  stm32f10x_tim.o
    0x08002c00   0x08002c00   0x0000005c   Code   RO          861    .text.TIM_OC4Init   stm32f10x_tim.o
    0x08002c5c   0x08002c5c   0x0000000e   Code   RO          939    .text.TIM_OC4PreloadConfig  stm32f10x_tim.o
    0x08002c6a   0x08002c6a   0x00000002   PAD
    0x08002c6c   0x08002c6c   0x00000004   Code   RO          995    .text.TIM_SetCompare1  stm32f10x_tim.o
    0x08002c70   0x08002c70   0x00000006   Code   RO         1001    .text.TIM_SetCompare4  stm32f10x_tim.o
    0x08002c76   0x08002c76   0x00000002   PAD
    0x08002c78   0x08002c78   0x000000aa   Code   RO          853    .text.TIM_TimeBaseInit  stm32f10x_tim.o
    0x08002d22   0x08002d22   0x00000002   PAD
    0x08002d24   0x08002d24   0x00000054   Code   RO         1271    .text.USART1_IRQHandler  usart.o
    0x08002d78   0x08002d78   0x000004a8   Code   RO         1269    .text.USART1_RxCpltCallback  usart.o
    0x08003220   0x08003220   0x00000032   Code   RO         1265    .text.USART1_SendString  usart.o
    0x08003252   0x08003252   0x00000002   PAD
    0x08003254   0x08003254   0x0000000e   Code   RO         1089    .text.USART_ClearITPendingBit  stm32f10x_usart.o
    0x08003262   0x08003262   0x00000002   PAD
    0x08003264   0x08003264   0x0000000a   Code   RO         1083    .text.USART_GetFlagStatus  stm32f10x_usart.o
    0x0800326e   0x0800326e   0x00000002   PAD
    0x08003270   0x08003270   0x0000002a   Code   RO         1087    .text.USART_GetITStatus  stm32f10x_usart.o
    0x0800329a   0x0800329a   0x00000002   PAD
    0x0800329c   0x0800329c   0x0000002e   Code   RO         1045    .text.USART_ITConfig  stm32f10x_usart.o
    0x080032ca   0x080032ca   0x00000002   PAD
    0x080032cc   0x080032cc   0x00000008   Code   RO         1061    .text.USART_ReceiveData  stm32f10x_usart.o
    0x080032d4   0x080032d4   0x00000008   Code   RO         1059    .text.USART_SendData  stm32f10x_usart.o
    0x080032dc   0x080032dc   0x00000002   Code   RO         1211    .text.UsageFault_Handler  stm32f10x_it.o
    0x080032de   0x080032de   0x00000002   PAD
    0x080032e0   0x080032e0   0x000000b6   Code   RO         1192    .text.main          main.o
    0x08003396   0x08003396   0x0000003e   Code   RO         1739    CL$$btod_d2e        c_w.l(btod.o)
    0x080033d4   0x080033d4   0x00000046   Code   RO         1741    CL$$btod_d2e_denorm_low  c_w.l(btod.o)
    0x0800341a   0x0800341a   0x00000060   Code   RO         1740    CL$$btod_d2e_norm_op1  c_w.l(btod.o)
    0x0800347a   0x0800347a   0x00000002   PAD
    0x0800347c   0x0800347c   0x00000338   Code   RO         1749    CL$$btod_div_common  c_w.l(btod.o)
    0x080037b4   0x080037b4   0x00000084   Code   RO         1747    CL$$btod_e2d        c_w.l(btod.o)
    0x08003838   0x08003838   0x000000dc   Code   RO         1746    CL$$btod_e2e        c_w.l(btod.o)
    0x08003914   0x08003914   0x0000002a   Code   RO         1743    CL$$btod_ediv       c_w.l(btod.o)
    0x0800393e   0x0800393e   0x0000002a   Code   RO         1745    CL$$btod_edivd      c_w.l(btod.o)
    0x08003968   0x08003968   0x0000002a   Code   RO         1742    CL$$btod_emul       c_w.l(btod.o)
    0x08003992   0x08003992   0x0000002a   Code   RO         1744    CL$$btod_emuld      c_w.l(btod.o)
    0x080039bc   0x080039bc   0x00000244   Code   RO         1748    CL$$btod_mult_common  c_w.l(btod.o)
    0x08003c00   0x08003c00   0x00000028   Code   RO         1779    i.__ARM_fpclassify  m_ws.l(fpclassify.o)
    0x08003c28   0x08003c28   0x0000000e   Code   RO         1685    i.__mathlib_dbl_overflow  m_ws.l(dunder.o)
    0x08003c36   0x08003c36   0x00000002   PAD
    0x08003c38   0x08003c38   0x00000010   Code   RO         1687    i.__mathlib_dbl_underflow  m_ws.l(dunder.o)
    0x08003c48   0x08003c48   0x00000010   Code   RO         1894    i.__mathlib_narrow  m_ws.l(narrow.o)
    0x08003c58   0x08003c58   0x000000bc   Code   RO         1895    i.__mathlib_tofloat  m_ws.l(narrow.o)
    0x08003d14   0x08003d14   0x00000016   Code   RO         1951    i.__support_ldexp   m_ws.l(ldexp.o)
    0x08003d2a   0x08003d2a   0x0000000e   Code   RO         1465    i._is_digit         c_w.l(__printf_wp.o)
    0x08003d38   0x08003d38   0x00000058   Code   RO         1922    i.frexp             m_ws.l(frexp.o)
    0x08003d90   0x08003d90   0x00000074   Code   RO         1952    i.ldexp             m_ws.l(ldexp.o)
    0x08003e04   0x08003e04   0x00000094   Code   RO         1552    i.round             m_ws.l(round.o)
    0x08003e98   0x08003e98   0x00000014   Code   RO         1555    i.strtof            m_ws.l(strtof.o)
    0x08003eac   0x08003eac   0x0000002c   Code   RO         1766    locale$$code        c_w.l(lc_ctype_c.o)
    0x08003ed8   0x08003ed8   0x0000002c   Code   RO         1769    locale$$code        c_w.l(lc_numeric_c.o)
    0x08003f04   0x08003f04   0x00000062   Code   RO         1490    x$fpl$d2f           fz_ws.l(d2f.o)
    0x08003f66   0x08003f66   0x00000002   PAD
    0x08003f68   0x08003f68   0x00000150   Code   RO         1492    x$fpl$dadd          fz_ws.l(daddsub_clz.o)
    0x080040b8   0x080040b8   0x00000010   Code   RO         1846    x$fpl$dcheck1       fz_ws.l(dcheck1.o)
    0x080040c8   0x080040c8   0x00000018   Code   RO         1771    x$fpl$dcmpinf       fz_ws.l(dcmpi.o)
    0x080040e0   0x080040e0   0x00000078   Code   RO         1645    x$fpl$deqf          fz_ws.l(deqf.o)
    0x08004158   0x08004158   0x0000005a   Code   RO         1508    x$fpl$dfixu         fz_ws.l(dfixu.o)
    0x080041b2   0x080041b2   0x00000026   Code   RO         1512    x$fpl$dfltu         fz_ws.l(dflt_clz.o)
    0x080041d8   0x080041d8   0x00000078   Code   RO         1647    x$fpl$dgeqf         fz_ws.l(dgeqf.o)
    0x08004250   0x08004250   0x00000078   Code   RO         1649    x$fpl$dleqf         fz_ws.l(dleqf.o)
    0x080042c8   0x080042c8   0x00000154   Code   RO         1518    x$fpl$dmul          fz_ws.l(dmul.o)
    0x0800441c   0x0800441c   0x0000009c   Code   RO         1651    x$fpl$dnaninf       fz_ws.l(dnaninf.o)
    0x080044b8   0x080044b8   0x0000000c   Code   RO         1653    x$fpl$dretinf       fz_ws.l(dretinf.o)
    0x080044c4   0x080044c4   0x0000006c   Code   RO         1655    x$fpl$drleqf        fz_ws.l(drleqf.o)
    0x08004530   0x08004530   0x000000b4   Code   RO         1657    x$fpl$drnd          fz_ws.l(drnd.o)
    0x080045e4   0x080045e4   0x00000016   Code   RO         1493    x$fpl$drsb          fz_ws.l(daddsub_clz.o)
    0x080045fa   0x080045fa   0x00000002   PAD
    0x080045fc   0x080045fc   0x000001dc   Code   RO         1494    x$fpl$dsub          fz_ws.l(daddsub_clz.o)
    0x080047d8   0x080047d8   0x00000056   Code   RO         1520    x$fpl$f2d           fz_ws.l(f2d.o)
    0x0800482e   0x0800482e   0x00000002   PAD
    0x08004830   0x08004830   0x000000c4   Code   RO         1522    x$fpl$fadd          fz_ws.l(faddsub_clz.o)
    0x080048f4   0x080048f4   0x00000054   Code   RO         1498    x$fpl$fcmp          fz_ws.l(dcmp.o)
    0x08004948   0x08004948   0x00000054   Code   RO         1528    x$fpl$fcmp          fz_ws.l(fcmp.o)
    0x0800499c   0x0800499c   0x00000018   Code   RO         1773    x$fpl$fcmpinf       fz_ws.l(fcmpi.o)
    0x080049b4   0x080049b4   0x00000184   Code   RO         1531    x$fpl$fdiv          fz_ws.l(fdiv.o)
    0x08004b38   0x08004b38   0x00000068   Code   RO         1659    x$fpl$feqf          fz_ws.l(feqf.o)
    0x08004ba0   0x08004ba0   0x00000036   Code   RO         1534    x$fpl$ffix          fz_ws.l(ffix.o)
    0x08004bd6   0x08004bd6   0x00000002   PAD
    0x08004bd8   0x08004bd8   0x00000030   Code   RO         1539    x$fpl$fflt          fz_ws.l(fflt_clz.o)
    0x08004c08   0x08004c08   0x00000026   Code   RO         1538    x$fpl$ffltu         fz_ws.l(fflt_clz.o)
    0x08004c2e   0x08004c2e   0x00000002   PAD
    0x08004c30   0x08004c30   0x00000068   Code   RO         1661    x$fpl$fgeqf         fz_ws.l(fgeqf.o)
    0x08004c98   0x08004c98   0x00000068   Code   RO         1663    x$fpl$fleqf         fz_ws.l(fleqf.o)
    0x08004d00   0x08004d00   0x00000102   Code   RO         1544    x$fpl$fmul          fz_ws.l(fmul.o)
    0x08004e02   0x08004e02   0x0000008c   Code   RO         1665    x$fpl$fnaninf       fz_ws.l(fnaninf.o)
    0x08004e8e   0x08004e8e   0x0000000a   Code   RO         1667    x$fpl$fretinf       fz_ws.l(fretinf.o)
    0x08004e98   0x08004e98   0x000000ea   Code   RO         1524    x$fpl$fsub          fz_ws.l(faddsub_clz.o)
    0x08004f82   0x08004f82   0x00000006   Code   RO         1775    x$fpl$ieeestatus    fz_ws.l(istatus.o)
    0x08004f88   0x08004f88   0x00000004   Code   RO         1546    x$fpl$printf1       fz_ws.l(printf1.o)
    0x08004f8c   0x08004f8c   0x00000064   Code   RO         1862    x$fpl$retnan        fz_ws.l(retnan.o)
    0x08004ff0   0x08004ff0   0x0000005c   Code   RO         1777    x$fpl$scalbn        fz_ws.l(scalbn.o)
    0x0800504c   0x0800504c   0x00000004   Code   RO         1848    x$fpl$scanf1        fz_ws.l(scanf1.o)
    0x08005050   0x08005050   0x00000008   Code   RO         1886    x$fpl$scanf2        fz_ws.l(scanf2.o)
    0x08005058   0x08005058   0x00000030   Code   RO         1892    x$fpl$trapveneer    fz_ws.l(trapv.o)
    0x08005088   0x08005088   0x00000000   Code   RO         1675    x$fpl$usenofp       fz_ws.l(usenofp.o)
    0x08005088   0x08005088   0x00000011   Data   RO         1473    .constdata          c_w.l(__printf_flags_ss_wp.o)
    0x08005099   0x08005099   0x00000003   PAD
    0x0800509c   0x0800509c   0x00000094   Data   RO         1737    .constdata          c_w.l(bigflt0.o)
    0x08005130   0x08005130   0x0000023a   Data   RO         1420    .rodata.OLED_F6x8   oled_data.o
    0x0800536a   0x0800536a   0x000005f0   Data   RO         1419    .rodata.OLED_F8x16  oled_data.o
    0x0800595a   0x0800595a   0x00000167   Data   RO         1279    .rodata.str1.1      usart.o
    0x08005ac1   0x08005ac1   0x00000003   PAD
    0x08005ac4   0x08005ac4   0x00000020   Data   RO         1983    Region$$Table       anon$$obj.o
    0x08005ae4   0x08005ae4   0x00000008   Data   RO         1882    c$$dinf             fz_ws.l(fpconst.o)
    0x08005aec   0x08005aec   0x00000008   Data   RO         1885    c$$dmax             fz_ws.l(fpconst.o)
    0x08005af4   0x08005af4   0x00000110   Data   RO         1765    locale$$data        c_w.l(lc_ctype_c.o)
    0x08005c04   0x08005c04   0x0000001c   Data   RO         1768    locale$$data        c_w.l(lc_numeric_c.o)


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08005c20, Size: 0x00000ad8, Max: 0x00005000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08005c20   0x00000002   Data   RW         1314    .data.EXTI2_IRQHandler.last_tim2_count  encoder.o
    0x20000002   0x08005c22   0x00000002   PAD
    0x20000004   0x08005c24   0x00000004   Data   RW         1136    .data.SystemCoreClock  system_stm32f10x.o
    0x20000008        -       0x00000060   Zero   RW         1697    .bss                c_w.l(libspace.o)
    0x20000068        -       0x00000400   Zero   RW         1410    .bss.OLED_DisplayBuf  oled.o
    0x20000468        -       0x00000004   Zero   RW         1315    .bss.TIM2_IRQHandler.last_absolute_count  encoder.o
    0x2000046c        -       0x00000020   Zero   RW         1277    .bss.USART1_RxCpltCallback.cmd_buffer  usart.o
    0x2000048c        -       0x00000001   Zero   RW         1278    .bss.USART1_RxCpltCallback.cmd_index  usart.o
    0x2000048d   0x08005c28   0x00000003   PAD
    0x20000490        -       0x00000018   Zero   RW         1313    .bss.encoder        encoder.o
    0x200004a8        -       0x00000004   Zero   RW         1280    .bss.g_set_value    usart.o
    0x200004ac        -       0x00000028   Zero   RW         1194    .bss.motor          main.o
    0x200004d4        -       0x00000002   Zero   RW         1252    .bss.pwmPeriod      pwm.o
    0x200004d6   0x08005c28   0x00000002   PAD
    0x200004d8        -       0x00000200   Zero   RW         1123    HEAP                startup_stm32f10x_md.o
    0x200006d8        -       0x00000400   Zero   RW         1122    STACK               startup_stm32f10x_md.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       678          0          0          2         28       6369   encoder.o
         4          0          0          0          0       2552   gpio_driver.o
       182          0          0          0         40       2566   main.o
        94          0          0          0          0       3136   misc.o
        86          0          0          0          0       1849   motor_control.o
      2026          0          0          0       1024      36453   oled.o
         0          0       2090          0          0        770   oled_data.o
       156          0          0          0          0       1355   pid.o
       390          6          0          0          2       4504   pwm.o
        64         26        236          0       1536        804   startup_stm32f10x_md.o
       146          0          0          0          0       2716   stm32f10x_exti.o
       264          0          0          0          0       5870   stm32f10x_gpio.o
        18          0          0          0          0        984   stm32f10x_it.o
        48          0          0          0          0       8197   stm32f10x_rcc.o
       780          0          0          0          0      29583   stm32f10x_tim.o
       128          0          0          0          0       8529   stm32f10x_usart.o
       272          0          0          4          0       2527   system_stm32f10x.o
       164          0          0          0          0       4028   timer.o
      1326        152        359          0         37       8291   usart.o

    ----------------------------------------------------------------------
      6898        <USER>       <GROUP>          8       2672     131083   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        72          0          3          2          5          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        64          6          0          0          0         84   __2snprintf.o
         8          0          0          0          0         68   __main.o
       392          4         17          0          0         92   __printf_flags_ss_wp.o
        14          0          0          0          0         68   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        94          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
        28          0          0          0          0         68   _chval.o
        48          6          0          0          0         96   _printf_char_common.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
         6          0          0          0          0          0   _printf_f.o
      1052          0          0          0          0        148   _printf_fp_dec.o
       128         16          0          0          0         84   _printf_fp_infnan.o
       178          0          0          0          0         88   _printf_intcommon.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
        22          0          0          0          0        100   _rserrno.o
        64          0          0          0          0         84   _sgetc.o
        16          0          0          0          0         68   _snputc.o
       158          0          0          0          0         92   _strtoul.o
       228          4        148          0          0         96   bigflt0.o
      2152        136          0          0          0        956   btod.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
        18          0          0          0          0         76   isspace.o
        44         10        272          0          0         76   lc_ctype_c.o
        44         10         28          0          0         76   lc_numeric_c.o
         2          0          0          0          0          0   libinit.o
        30          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
        38          0          0          0          0         68   llshl.o
       138          0          0          0          0         80   lludiv10.o
        88          0          0          0          0         76   memcmp.o
        16          4          0          0          0         76   rt_ctype_table.o
         8          4          0          0          0         68   rt_errno_addr_intlibspace.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
        78          0          0          0          0         80   rt_memclr_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
      1272         16          0          0          0        168   scanf_fp.o
       800         14          0          0          0        100   scanf_hexfp.o
       308         16          0          0          0        100   scanf_infnan.o
       128          0          0          0          0         68   strcmpv7m.o
       176         14          0          0          0        124   strtod.o
         8          0          0          0          0         68   strtof.o
       112          0          0          0          0         88   strtol.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
        98          4          0          0          0         92   d2f.o
       834         16          0          0          0        372   daddsub_clz.o
        16          4          0          0          0         68   dcheck1.o
        84          0          0          0          0        196   dcmp.o
        24          0          0          0          0         68   dcmpi.o
       120          4          0          0          0         92   deqf.o
        90          4          0          0          0         92   dfixu.o
        38          0          0          0          0         68   dflt_clz.o
       120          4          0          0          0         92   dgeqf.o
       120          4          0          0          0         92   dleqf.o
       340         12          0          0          0        104   dmul.o
       156          4          0          0          0         92   dnaninf.o
        12          0          0          0          0         68   dretinf.o
       108          0          0          0          0         80   drleqf.o
       180          4          0          0          0         80   drnd.o
        86          4          0          0          0         84   f2d.o
       430          8          0          0          0        168   faddsub_clz.o
        84          0          0          0          0        196   fcmp.o
        24          0          0          0          0         68   fcmpi.o
       388         76          0          0          0         96   fdiv.o
       104          4          0          0          0         84   feqf.o
        54          4          0          0          0         84   ffix.o
        86          0          0          0          0        136   fflt_clz.o
       104          4          0          0          0         84   fgeqf.o
       104          4          0          0          0         84   fleqf.o
       258          4          0          0          0         84   fmul.o
       140          4          0          0          0         84   fnaninf.o
         0          0         16          0          0          0   fpconst.o
        10          0          0          0          0         68   fretinf.o
         6          0          0          0          0         68   istatus.o
         4          0          0          0          0         68   printf1.o
       100          0          0          0          0         68   retnan.o
        92          0          0          0          0         68   scalbn.o
         4          0          0          0          0         68   scanf1.o
         8          0          0          0          0         84   scanf2.o
        48          0          0          0          0         68   trapv.o
         0          0          0          0          0          0   usenofp.o
        30          6          0          0          0        136   dunder.o
        40          0          0          0          0         68   fpclassify.o
        88          8          0          0          0         76   frexp.o
       138          0          0          0          0        160   ldexp.o
       204          4          0          0          0        168   narrow.o
       148         12          0          0          0         88   round.o
        20          0          0          0          0         76   strtof.o

    ----------------------------------------------------------------------
     13482        <USER>        <GROUP>          0         96       8500   Library Totals
        34          0          3          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      8306        296        465          0         96       4260   c_w.l
      4474        172         16          0          0       3468   fz_ws.l
       668         30          0          0          0        772   m_ws.l

    ----------------------------------------------------------------------
     13482        <USER>        <GROUP>          0         96       8500   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     20380        682       3204          8       2768     134491   Grand Totals
     20380        682       3204          8       2768     134491   ELF Image Totals
     20380        682       3204          8          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                23584 (  23.03kB)
    Total RW  Size (RW Data + ZI Data)              2776 (   2.71kB)
    Total ROM Size (Code + RO Data + RW Data)      23592 (  23.04kB)

==============================================================================

